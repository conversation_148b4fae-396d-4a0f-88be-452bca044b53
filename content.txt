/
├── public/                   # 静态资源 (HTML, CSS, 图片等)
├── src/                      # 源代码
│   ├── assets/               # 项目静态资源 (图片, 字体等)
│   ├── components/           # 可复用的 UI 组件
│   │   ├── common/           # 通用基础组件 (Button, Input, Modal等)
│   │   └── layout/           # 布局组件 (Header, Footer, Sidebar等)
│   ├── config/               # 项目配置 (API 地址, 主题颜色等)
│   ├── constants/            # 常量
│   ├── features/             # 功能模块 (按业务功能划分)
│   │   ├── auth/             # 认证模块 (登录, 注册)
│   │   │   ├── components/
│   │   │   ├── hooks/
│   │   │   ├── services/
│   │   │   ├── store/
│   │   │   └── views/        # 或 pages/
│   │   └── satellite-tracking/ # 另一个功能模块示例
│   │       ├── components/
│   │       ├── hooks/
│   │       ├── services/
│   │       ├── store/
│   │       └── views/        # 或 pages/
│   ├── hooks/                # 自定义 Hooks
│   ├── layouts/              # 页面布局 (如果不用上面的 layout 组件)
│   ├── lib/                  # 第三方库或工具的封装
│   ├── pages/                # 页面级别组件 (如果不用 feature-based 的 views)
│   ├── services/             # API 服务 (HTTP 请求等)
│   ├── store/                # 状态管理 (如 Redux, Zustand, Pinia)
│   │   ├── index.ts          # store 入口
│   │   └── modules/          # 按模块划分的 store
│   ├── styles/               # 全局样式和主题
│   │   ├── base/             # 基础样式 (reset, normalize)
│   │   ├── themes/           # 主题
│   │   └── utils/            # SASS/LESS mixins, functions
│   ├── types/                # TypeScript 类型定义
│   ├── utils/                # 通用工具函数
│   └── main.tsx              # (或 main.js) 应用入口文件
├── tests/                    # 测试文件
│   ├── unit/                 # 单元测试
│   ├── integration/          # 集成测试
│   └── e2e/                  # 端到端测试
├── scripts/                  # 构建脚本, CI/CD 脚本等
├── config/                   # 项目构建配置文件 (如 Webpack, Vite, Babel)
├── .vscode/                  # VSCode 编辑器配置
├── .editorconfig             # 编辑器通用配置
├── .env.development          # 开发环境变量
├── .env.production           # 生产环境变量
├── .eslintignore             # ESLint 忽略配置
├── .eslintrc.js              # (或 .json, .yaml) ESLint 配置
├── .gitignore                # Git 忽略配置
├── .prettierignore           # Prettier 忽略配置
├── .prettierrc.js            # (或 .json, .yaml) Prettier 配置
├── babel.config.js           # (如果使用 Babel)
├── jest.config.js            # (如果使用 Jest)
├── package.json
├── tsconfig.json
└── README.md