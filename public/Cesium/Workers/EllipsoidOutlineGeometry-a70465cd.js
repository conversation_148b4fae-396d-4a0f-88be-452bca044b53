/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.96
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */
define(["exports","./Transforms-c450597e","./Matrix2-21f90abf","./ComponentDatatype-4028c72d","./defaultValue-4607806f","./RuntimeError-cef79f54","./GeometryAttribute-3c090c07","./GeometryAttributes-acac33d2","./GeometryOffsetAttribute-3e5f3e97","./IndexDatatype-20e78e57"],(function(t,i,e,n,a,o,r,s,u,m){"use strict";const f=new e.Cartesian3(1,1,1),c=Math.cos,l=Math.sin;function d(t){t=a.defaultValue(t,a.defaultValue.EMPTY_OBJECT);const i=a.defaultValue(t.radii,f),o=a.defaultValue(t.innerRadii,i),r=a.defaultValue(t.minimumClock,0),s=a.defaultValue(t.maximumClock,n.CesiumMath.TWO_PI),u=a.defaultValue(t.minimumCone,0),m=a.defaultValue(t.maximumCone,n.CesiumMath.PI),c=Math.round(a.defaultValue(t.stackPartitions,10)),l=Math.round(a.defaultValue(t.slicePartitions,8)),d=Math.round(a.defaultValue(t.subdivisions,128));this._radii=e.Cartesian3.clone(i),this._innerRadii=e.Cartesian3.clone(o),this._minimumClock=r,this._maximumClock=s,this._minimumCone=u,this._maximumCone=m,this._stackPartitions=c,this._slicePartitions=l,this._subdivisions=d,this._offsetAttribute=t.offsetAttribute,this._workerName="createEllipsoidOutlineGeometry"}d.packedLength=2*e.Cartesian3.packedLength+8,d.pack=function(t,i,n){return n=a.defaultValue(n,0),e.Cartesian3.pack(t._radii,i,n),n+=e.Cartesian3.packedLength,e.Cartesian3.pack(t._innerRadii,i,n),n+=e.Cartesian3.packedLength,i[n++]=t._minimumClock,i[n++]=t._maximumClock,i[n++]=t._minimumCone,i[n++]=t._maximumCone,i[n++]=t._stackPartitions,i[n++]=t._slicePartitions,i[n++]=t._subdivisions,i[n]=a.defaultValue(t._offsetAttribute,-1),i};const C=new e.Cartesian3,_=new e.Cartesian3,p={radii:C,innerRadii:_,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0,offsetAttribute:void 0};d.unpack=function(t,i,n){i=a.defaultValue(i,0);const o=e.Cartesian3.unpack(t,i,C);i+=e.Cartesian3.packedLength;const r=e.Cartesian3.unpack(t,i,_);i+=e.Cartesian3.packedLength;const s=t[i++],u=t[i++],m=t[i++],f=t[i++],c=t[i++],l=t[i++],h=t[i++],y=t[i];return a.defined(n)?(n._radii=e.Cartesian3.clone(o,n._radii),n._innerRadii=e.Cartesian3.clone(r,n._innerRadii),n._minimumClock=s,n._maximumClock=u,n._minimumCone=m,n._maximumCone=f,n._stackPartitions=c,n._slicePartitions=l,n._subdivisions=h,n._offsetAttribute=-1===y?void 0:y,n):(p.minimumClock=s,p.maximumClock=u,p.minimumCone=m,p.maximumCone=f,p.stackPartitions=c,p.slicePartitions=l,p.subdivisions=h,p.offsetAttribute=-1===y?void 0:y,new d(p))},d.createGeometry=function(t){const o=t._radii;if(o.x<=0||o.y<=0||o.z<=0)return;const f=t._innerRadii;if(f.x<=0||f.y<=0||f.z<=0)return;const d=t._minimumClock,C=t._maximumClock,_=t._minimumCone,p=t._maximumCone,h=t._subdivisions,y=e.Ellipsoid.fromCartesian3(o);let k=t._slicePartitions+1,b=t._stackPartitions+1;k=Math.round(k*Math.abs(C-d)/n.CesiumMath.TWO_PI),b=Math.round(b*Math.abs(p-_)/n.CesiumMath.PI),k<2&&(k=2),b<2&&(b=2);let x=0,A=1;const P=f.x!==o.x||f.y!==o.y||f.z!==o.z;let v=!1,M=!1;P&&(A=2,_>0&&(v=!0,x+=k),p<Math.PI&&(M=!0,x+=k));const w=h*A*(b+k),V=new Float64Array(3*w),g=2*(w+x-(k+b)*A),E=m.IndexDatatype.createTypedArray(w,g);let G,O,D,I,T=0;const z=new Array(b),L=new Array(b);for(G=0;G<b;G++)I=_+G*(p-_)/(b-1),z[G]=l(I),L[G]=c(I);const R=new Array(h),N=new Array(h);for(G=0;G<h;G++)D=d+G*(C-d)/(h-1),R[G]=l(D),N[G]=c(D);for(G=0;G<b;G++)for(O=0;O<h;O++)V[T++]=o.x*z[G]*N[O],V[T++]=o.y*z[G]*R[O],V[T++]=o.z*L[G];if(P)for(G=0;G<b;G++)for(O=0;O<h;O++)V[T++]=f.x*z[G]*N[O],V[T++]=f.y*z[G]*R[O],V[T++]=f.z*L[G];for(z.length=h,L.length=h,G=0;G<h;G++)I=_+G*(p-_)/(h-1),z[G]=l(I),L[G]=c(I);for(R.length=k,N.length=k,G=0;G<k;G++)D=d+G*(C-d)/(k-1),R[G]=l(D),N[G]=c(D);for(G=0;G<h;G++)for(O=0;O<k;O++)V[T++]=o.x*z[G]*N[O],V[T++]=o.y*z[G]*R[O],V[T++]=o.z*L[G];if(P)for(G=0;G<h;G++)for(O=0;O<k;O++)V[T++]=f.x*z[G]*N[O],V[T++]=f.y*z[G]*R[O],V[T++]=f.z*L[G];for(T=0,G=0;G<b*A;G++){const t=G*h;for(O=0;O<h-1;O++)E[T++]=t+O,E[T++]=t+O+1}let B=b*h*A;for(G=0;G<k;G++)for(O=0;O<h-1;O++)E[T++]=B+G+O*k,E[T++]=B+G+(O+1)*k;if(P)for(B=b*h*A+k*h,G=0;G<k;G++)for(O=0;O<h-1;O++)E[T++]=B+G+O*k,E[T++]=B+G+(O+1)*k;if(P){let t=b*h*A,i=t+h*k;if(v)for(G=0;G<k;G++)E[T++]=t+G,E[T++]=i+G;if(M)for(t+=h*k-k,i+=h*k-k,G=0;G<k;G++)E[T++]=t+G,E[T++]=i+G}const S=new s.GeometryAttributes({position:new r.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:V})});if(a.defined(t._offsetAttribute)){const i=V.length,e=t._offsetAttribute===u.GeometryOffsetAttribute.NONE?0:1,a=new Uint8Array(i/3).fill(e);S.applyOffset=new r.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}return new r.Geometry({attributes:S,indices:E,primitiveType:r.PrimitiveType.LINES,boundingSphere:i.BoundingSphere.fromEllipsoid(y),offsetAttribute:t._offsetAttribute})},t.EllipsoidOutlineGeometry=d}));
