import React from 'react';
import Box from '../components/main/box';
import '../components/main/css/box.css';
import '../components/main/css/cesium.css';

const BoxPreview: React.FC = () => {
  const sampleData = [
    { id: 1, name: '卫星A', status: '正常', signal: '85%' },
    { id: 2, name: '卫星B', status: '警告', signal: '72%' },
    { id: 3, name: '卫星C', status: '正常', signal: '91%' },
  ];

  const SampleTable = () => (
    <div style={{ color: '#ffffff' }}>
      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
        <thead>
          <tr>
            <th style={{ padding: '8px', borderBottom: '1px solid rgba(255,255,255,0.2)', color: '#ffffff' }}>名称</th>
            <th style={{ padding: '8px', borderBottom: '1px solid rgba(255,255,255,0.2)', color: '#ffffff' }}>状态</th>
            <th style={{ padding: '8px', borderBottom: '1px solid rgba(255,255,255,0.2)', color: '#ffffff' }}>信号</th>
          </tr>
        </thead>
        <tbody>
          {sampleData.map(item => (
            <tr key={item.id}>
              <td style={{ padding: '8px', borderBottom: '1px solid rgba(255,255,255,0.1)', color: '#ffffff' }}>{item.name}</td>
              <td style={{ padding: '8px', borderBottom: '1px solid rgba(255,255,255,0.1)', color: '#ffffff' }}>{item.status}</td>
              <td style={{ padding: '8px', borderBottom: '1px solid rgba(255,255,255,0.1)', color: '#ffffff' }}>{item.signal}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const SampleChart = () => (
    <div style={{ 
      height: '120px', 
      background: 'rgba(255,255,255,0.05)', 
      borderRadius: '4px', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      color: '#ffffff'
    }}>
      <div>图表区域示例</div>
    </div>
  );

  const SampleInfo = () => (
    <div style={{ color: '#ffffff' }}>
      <p style={{ margin: '8px 0', fontSize: '14px' }}>卫星名称: Satellite-001</p>
      <p style={{ margin: '8px 0', fontSize: '14px' }}>发射时间: 2021-08</p>
      <p style={{ margin: '8px 0', fontSize: '14px' }}>服务状态: 正常运行</p>
      <p style={{ margin: '8px 0', fontSize: '14px' }}>活动状态: 稳定</p>
    </div>
  );

  return (
    <div style={{ 
      background: 'linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%)', 
      minHeight: '100vh', 
      padding: '20px' 
    }}>
      <h1 style={{ color: '#ffffff', textAlign: 'center', marginBottom: '30px' }}>
        Box组件样式预览
      </h1>
      
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
        gap: '20px',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        <Box 
          title="卫星信息列表" 
          component={<SampleTable />} 
        />
        
        <Box 
          title="网络状态图表" 
          component={<SampleChart />} 
        />
        
        <Box 
          title="卫星详细信息" 
          component={<SampleInfo />} 
        />
        
        <Box 
          title="系统监控" 
          component={
            <div style={{ color: '#ffffff' }}>
              <div style={{ marginBottom: '10px' }}>
                <span style={{ color: '#00d4ff' }}>CPU使用率:</span> 45%
              </div>
              <div style={{ marginBottom: '10px' }}>
                <span style={{ color: '#00d4ff' }}>内存使用率:</span> 67%
              </div>
              <div style={{ marginBottom: '10px' }}>
                <span style={{ color: '#00d4ff' }}>网络延迟:</span> 12ms
              </div>
            </div>
          } 
        />
      </div>
    </div>
  );
};

export default BoxPreview; 