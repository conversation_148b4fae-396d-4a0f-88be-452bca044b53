import React, { useState, useEffect } from 'react';
import {
  getConstellationList,
  getTerminalList,
  getStatus,
  getCZMLData
} from '../components/utils/api/getListAPI';
import {
  setSelectedConstellation,
  getTerminalPathCzml,
  postCurrentTime,
  clearSelectedConstellation,
  attachCommand,
  getCoreLog,
  setSimulationParameters
} from '../components/utils/api/postAPI';
import CoreNetworkLogsPreview from './CoreNetworkLogsPreview';
import BoxPreview from './BoxPreview';
import '../components/main/css/box.css';

interface TestState {
  loading: boolean;
  error: string | null;
  apiResults: {
    constellation: any;
    terminal: any;
    status: any;
    czmlData: any;
    terminalPath: any;
    coreLog: any;
    attachCmd: any;
    simulationParams: any;
  };
  testParams: {
    selectedConstellation: number;
    terminalA: number;
    terminalB: number;
    currentTime: number;
    selectedNF: string;
    terminalId: number;
  };
}

const ComprehensiveTestPage: React.FC = () => {
  const [testState, setTestState] = useState<TestState>({
    loading: false,
    error: null,
    apiResults: {
      constellation: null,
      terminal: null,
      status: null,
      czmlData: null,
      terminalPath: null,
      coreLog: null,
      attachCmd: null,
      simulationParams: null,
    },
    testParams: {
      selectedConstellation: 1,
      terminalA: 0,
      terminalB: 1,
      currentTime: 1000000000,
      selectedNF: 'smf',
      terminalId: 1,
    }
  });

  const [activeTab, setActiveTab] = useState<'api' | 'components' | 'simulation'>('api');

  // 核心网网元选项
  const nfOptions = [
    { value: 'smf', label: 'SMF (会话管理功能)' },
    { value: 'amf', label: 'AMF (接入和移动性管理)' },
    { value: 'upf', label: 'UPF (用户面功能)' },
    { value: 'pcf', label: 'PCF (策略控制功能)' },
    { value: 'udm', label: 'UDM (统一数据管理)' },
    { value: 'nrf', label: 'NRF (网络存储功能)' }
  ];

  // 样式定义
  const containerStyle: React.CSSProperties = {
    background: 'linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%)',
    minHeight: '100vh',
    padding: '20px',
    fontFamily: 'Arial, sans-serif',
    color: '#ffffff'
  };

  const cardStyle: React.CSSProperties = {
    background: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '12px',
    padding: '20px',
    margin: '20px 0',
    color: '#ffffff'
  };

  const buttonStyle: React.CSSProperties = {
    background: 'linear-gradient(45deg, #1890ff, #36cfc9)',
    border: 'none',
    borderRadius: '6px',
    color: 'white',
    padding: '10px 20px',
    margin: '5px',
    cursor: 'pointer',
    fontSize: '14px',
    transition: 'all 0.3s ease',
    minWidth: '120px'
  };

  const inputStyle: React.CSSProperties = {
    background: 'rgba(255, 255, 255, 0.1)',
    border: '1px solid rgba(255, 255, 255, 0.3)',
    borderRadius: '4px',
    color: '#ffffff',
    padding: '8px 12px',
    fontSize: '14px',
    margin: '5px',
    width: '150px'
  };

  const resultStyle: React.CSSProperties = {
    background: 'rgba(0, 0, 0, 0.3)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    borderRadius: '6px',
    padding: '15px',
    marginTop: '15px',
    maxHeight: '300px',
    overflow: 'auto',
    fontSize: '12px',
    fontFamily: 'monospace'
  };

  const tabStyle = (isActive: boolean): React.CSSProperties => ({
    ...buttonStyle,
    background: isActive 
      ? 'linear-gradient(45deg, #1890ff, #36cfc9)' 
      : 'rgba(255, 255, 255, 0.1)',
    opacity: isActive ? 1 : 0.7
  });

  // API测试函数
  const testBasicAPIs = async () => {
    setTestState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const [constellation, terminal, status] = await Promise.all([
        getConstellationList(),
        getTerminalList(),
        getStatus()
      ]);

      setTestState(prev => ({
        ...prev,
        apiResults: {
          ...prev.apiResults,
          constellation,
          terminal,
          status
        },
        loading: false
      }));
    } catch (error: any) {
      setTestState(prev => ({
        ...prev,
        error: `基础API测试失败: ${error.message}`,
        loading: false
      }));
    }
  };

  const testConstellationAPI = async () => {
    setTestState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const result = await setSelectedConstellation(testState.testParams.selectedConstellation);
      
      // 等待一下再获取CZML数据
      setTimeout(async () => {
        try {
          const czmlData = await getCZMLData();
          setTestState(prev => ({
            ...prev,
            apiResults: {
              ...prev.apiResults,
              czmlData: { setResult: result, czmlData }
            },
            loading: false
          }));
        } catch (error: any) {
          setTestState(prev => ({
            ...prev,
            error: `获取CZML数据失败: ${error.message}`,
            loading: false
          }));
        }
      }, 500);
    } catch (error: any) {
      setTestState(prev => ({
        ...prev,
        error: `星座设置失败: ${error.message}`,
        loading: false
      }));
    }
  };

  const testTerminalPathAPI = async () => {
    setTestState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const result = await getTerminalPathCzml(
        testState.testParams.terminalA,
        testState.testParams.terminalB
      );
      setTestState(prev => ({
        ...prev,
        apiResults: {
          ...prev.apiResults,
          terminalPath: result
        },
        loading: false
      }));
    } catch (error: any) {
      setTestState(prev => ({
        ...prev,
        error: `终端路径测试失败: ${error.message}`,
        loading: false
      }));
    }
  };

  const testCoreLogAPI = async () => {
    setTestState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const result = await getCoreLog(testState.testParams.selectedNF);
      setTestState(prev => ({
        ...prev,
        apiResults: {
          ...prev.apiResults,
          coreLog: result
        },
        loading: false
      }));
    } catch (error: any) {
      setTestState(prev => ({
        ...prev,
        error: `核心网日志测试失败: ${error.message}`,
        loading: false
      }));
    }
  };

  const testAttachCommandAPI = async () => {
    setTestState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const result = await attachCommand(testState.testParams.terminalId);
      setTestState(prev => ({
        ...prev,
        apiResults: {
          ...prev.apiResults,
          attachCmd: result
        },
        loading: false
      }));
    } catch (error: any) {
      setTestState(prev => ({
        ...prev,
        error: `连接命令测试失败: ${error.message}`,
        loading: false
      }));
    }
  };

  const testSimulationParametersAPI = async () => {
    setTestState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const kvmList = [
        { kvm_no: 1, node_no: 1, node_type: "gs" as const },
        { kvm_no: 2, node_no: 3, node_type: "gs" as const }
      ];
      const result = await setSimulationParameters(kvmList, 1, testState.testParams.currentTime, 0, 0);
      setTestState(prev => ({
        ...prev,
        apiResults: {
          ...prev.apiResults,
          simulationParams: result
        },
        loading: false
      }));
    } catch (error: any) {
      setTestState(prev => ({
        ...prev,
        error: `模拟参数设置失败: ${error.message}`,
        loading: false
      }));
    }
  };

  const clearAllResults = () => {
    setTestState(prev => ({
      ...prev,
      apiResults: {
        constellation: null,
        terminal: null,
        status: null,
        czmlData: null,
        terminalPath: null,
        coreLog: null,
        attachCmd: null,
        simulationParams: null,
      },
      error: null
    }));
  };

  const downloadJSON = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}_${new Date().getTime()}.json`;
    document.body.appendChild(link);
    link.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(link);
  };

  return (
    <div style={containerStyle}>
      <h1 style={{ textAlign: 'center', marginBottom: '30px', color: '#36cfc9' }}>
        🧪 综合测试页面
      </h1>

      {/* 标签页导航 */}
      <div style={{ textAlign: 'center', marginBottom: '30px' }}>
        <button 
          style={tabStyle(activeTab === 'api')}
          onClick={() => setActiveTab('api')}
        >
          📡 API 测试
        </button>
        <button 
          style={tabStyle(activeTab === 'simulation')}
          onClick={() => setActiveTab('simulation')}
        >
          🚀 模拟参数测试
        </button>
        <button 
          style={tabStyle(activeTab === 'components')}
          onClick={() => setActiveTab('components')}
        >
          🎨 组件预览
        </button>
      </div>

      {/* 错误显示 */}
      {testState.error && (
        <div style={{
          ...cardStyle,
          background: 'rgba(255, 0, 0, 0.1)',
          border: '1px solid rgba(255, 0, 0, 0.3)',
          color: '#ff4d4f'
        }}>
          <h3>❌ 错误信息:</h3>
          <p>{testState.error}</p>
        </div>
      )}

      {/* API测试标签页 */}
      {activeTab === 'api' && (
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {/* 基础API测试 */}
          <div style={cardStyle}>
            <h2 style={{ color: '#36cfc9', marginBottom: '20px' }}>
              📊 基础API测试
            </h2>
            <button 
              style={buttonStyle}
              onClick={testBasicAPIs}
              disabled={testState.loading}
            >
              {testState.loading ? '测试中...' : '测试基础APIs'}
            </button>
            {testState.apiResults.constellation && (
              <div style={resultStyle}>
                <h4>🛰️ 星座列表:</h4>
                <pre>{JSON.stringify(testState.apiResults.constellation, null, 2)}</pre>
              </div>
            )}
            {testState.apiResults.terminal && (
              <div style={resultStyle}>
                <h4>📡 终端列表:</h4>
                <pre>{JSON.stringify(testState.apiResults.terminal, null, 2)}</pre>
              </div>
            )}
            {testState.apiResults.status && (
              <div style={resultStyle}>
                <h4>📈 系统状态:</h4>
                <pre>{JSON.stringify(testState.apiResults.status, null, 2)}</pre>
              </div>
            )}
          </div>

          {/* 星座和CZML测试 */}
          <div style={cardStyle}>
            <h2 style={{ color: '#36cfc9', marginBottom: '20px' }}>
              🛰️ 星座设置与CZML测试
            </h2>
            <div style={{ marginBottom: '15px' }}>
              <label>星座编号: </label>
              <input
                type="number"
                value={testState.testParams.selectedConstellation}
                onChange={(e) => setTestState(prev => ({
                  ...prev,
                  testParams: { ...prev.testParams, selectedConstellation: parseInt(e.target.value) }
                }))}
                style={inputStyle}
                min="1"
                max="10"
              />
              <button 
                style={buttonStyle}
                onClick={testConstellationAPI}
                disabled={testState.loading}
              >
                设置星座并获取CZML
              </button>
            </div>
            {testState.apiResults.czmlData && (
              <div style={resultStyle}>
                <h4>🌍 CZML数据结果:</h4>
                <button 
                  style={{...buttonStyle, fontSize: '12px', padding: '5px 10px'}}
                  onClick={() => downloadJSON(testState.apiResults.czmlData, 'czml_data')}
                >
                  下载CZML数据
                </button>
                <pre>{JSON.stringify(testState.apiResults.czmlData, null, 2).substring(0, 500)}...</pre>
              </div>
            )}
          </div>

          {/* 终端路径测试 */}
          <div style={cardStyle}>
            <h2 style={{ color: '#36cfc9', marginBottom: '20px' }}>
              🔗 终端路径测试
            </h2>
            <div style={{ marginBottom: '15px' }}>
              <label>起始终端: </label>
              <input
                type="number"
                value={testState.testParams.terminalA}
                onChange={(e) => setTestState(prev => ({
                  ...prev,
                  testParams: { ...prev.testParams, terminalA: parseInt(e.target.value) }
                }))}
                style={inputStyle}
                min="0"
              />
              <label>目标终端: </label>
              <input
                type="number"
                value={testState.testParams.terminalB}
                onChange={(e) => setTestState(prev => ({
                  ...prev,
                  testParams: { ...prev.testParams, terminalB: parseInt(e.target.value) }
                }))}
                style={inputStyle}
                min="0"
              />
              <button 
                style={buttonStyle}
                onClick={testTerminalPathAPI}
                disabled={testState.loading}
              >
                获取终端路径
              </button>
            </div>
            {testState.apiResults.terminalPath && (
              <div style={resultStyle}>
                <h4>🛤️ 终端路径CZML:</h4>
                <button 
                  style={{...buttonStyle, fontSize: '12px', padding: '5px 10px'}}
                  onClick={() => downloadJSON(testState.apiResults.terminalPath, 'terminal_path')}
                >
                  下载路径数据
                </button>
                <pre>{JSON.stringify(testState.apiResults.terminalPath, null, 2).substring(0, 500)}...</pre>
              </div>
            )}
          </div>

          {/* 核心网日志测试 */}
          <div style={cardStyle}>
            <h2 style={{ color: '#36cfc9', marginBottom: '20px' }}>
              📋 核心网日志测试
            </h2>
            <div style={{ marginBottom: '15px' }}>
              <label>网络功能: </label>
              <select 
                value={testState.testParams.selectedNF}
                onChange={(e) => setTestState(prev => ({
                  ...prev,
                  testParams: { ...prev.testParams, selectedNF: e.target.value }
                }))}
                style={inputStyle}
              >
                {nfOptions.map(option => (
                  <option key={option.value} value={option.value} style={{ background: '#1a2332' }}>
                    {option.label}
                  </option>
                ))}
              </select>
              <button 
                style={buttonStyle}
                onClick={testCoreLogAPI}
                disabled={testState.loading}
              >
                获取核心网日志
              </button>
            </div>
            {testState.apiResults.coreLog && (
              <div style={resultStyle}>
                <h4>📝 核心网日志:</h4>
                <pre>{testState.apiResults.coreLog}</pre>
              </div>
            )}
          </div>

          {/* 连接命令测试 */}
          <div style={cardStyle}>
            <h2 style={{ color: '#36cfc9', marginBottom: '20px' }}>
              🔧 连接命令测试
            </h2>
            <div style={{ marginBottom: '15px' }}>
              <label>终端ID: </label>
              <input
                type="number"
                value={testState.testParams.terminalId}
                onChange={(e) => setTestState(prev => ({
                  ...prev,
                  testParams: { ...prev.testParams, terminalId: parseInt(e.target.value) }
                }))}
                style={inputStyle}
                min="1"
              />
              <button 
                style={buttonStyle}
                onClick={testAttachCommandAPI}
                disabled={testState.loading}
              >
                获取连接命令
              </button>
            </div>
            {testState.apiResults.attachCmd && (
              <div style={resultStyle}>
                <h4>⚡ 连接命令:</h4>
                <pre>{testState.apiResults.attachCmd}</pre>
              </div>
            )}
          </div>

          {/* 清除按钮 */}
          <div style={{ textAlign: 'center', marginTop: '30px' }}>
            <button 
              style={{...buttonStyle, background: 'linear-gradient(45deg, #ff4d4f, #ff7875)'}}
              onClick={clearAllResults}
            >
              🗑️ 清除所有结果
            </button>
          </div>
        </div>
      )}

      {/* 模拟参数测试标签页 */}
      {activeTab === 'simulation' && (
        <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
          <div style={cardStyle}>
            <h2 style={{ color: '#36cfc9', marginBottom: '20px' }}>
              🚀 模拟参数设置测试
            </h2>
            <div style={{ marginBottom: '15px' }}>
              <label>当前时间 (ns): </label>
              <input
                type="number"
                value={testState.testParams.currentTime}
                onChange={(e) => setTestState(prev => ({
                  ...prev,
                  testParams: { ...prev.testParams, currentTime: parseInt(e.target.value) }
                }))}
                style={inputStyle}
              />
              <button 
                style={buttonStyle}
                onClick={testSimulationParametersAPI}
                disabled={testState.loading}
              >
                设置模拟参数
              </button>
            </div>
            <div style={{ padding: '15px', background: 'rgba(255, 255, 255, 0.05)', borderRadius: '6px' }}>
              <h4>当前配置:</h4>
              <p>• KVM列表: [{"{"}"kvm_no":1, "node_no":1, "node_type":"gs"{"}"}, {"{"}"kvm_no":2, "node_no":3, "node_type":"gs"{"}"}]</p>
              <p>• 模式: 1 (透明)</p>
              <p>• 时间: {testState.testParams.currentTime} ns</p>
              <p>• 扩展节点: 0</p>
              <p>• 启动: 0 (不启动)</p>
            </div>
            {testState.apiResults.simulationParams && (
              <div style={resultStyle}>
                <h4>✅ 模拟参数设置结果:</h4>
                <pre>{JSON.stringify(testState.apiResults.simulationParams, null, 2)}</pre>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 组件预览标签页 */}
      {activeTab === 'components' && (
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <div style={cardStyle}>
            <h2 style={{ color: '#36cfc9', marginBottom: '20px' }}>
              🎨 组件预览
            </h2>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
              <div>
                <h3 style={{ color: '#1890ff' }}>核心网日志组件</h3>
                <CoreNetworkLogsPreview />
              </div>
              <div>
                <h3 style={{ color: '#1890ff' }}>Box组件预览</h3>
                <BoxPreview />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 加载指示器 */}
      {testState.loading && (
        <div style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          background: 'rgba(0, 0, 0, 0.8)',
          padding: '20px',
          borderRadius: '10px',
          color: '#ffffff',
          zIndex: 1000
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid rgba(255, 255, 255, 0.1)',
              borderLeft: '4px solid #36cfc9',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 10px'
            }}></div>
            <p>正在执行测试...</p>
          </div>
        </div>
      )}

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default ComprehensiveTestPage; 