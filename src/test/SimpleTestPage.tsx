import React, { useState, useEffect } from 'react';
import { getCoreLog } from '../components/utils/api/postAPI';
import { getConstellationList, getTerminalList } from '../components/utils/api/getListAPI';
import CoreNetworkLogsPreview from './CoreNetworkLogsPreview';
import '../components/main/css/box.css';

interface TestState {
  coreLogResult: any;
  constellationData: any;
  terminalData: any;
  loading: boolean;
  error: string | null;
  selectedNF: string;
}

const SimpleTestPage: React.FC = () => {
  const [testState, setTestState] = useState<TestState>({
    coreLogResult: null,
    constellationData: null,
    terminalData: null,
    loading: false,
    error: null,
    selectedNF: 'smf'
  });

  const [activeTab, setActiveTab] = useState<'api' | 'components'>('api');

  // 核心网网元选项
  const nfOptions = [
    { value: 'smf', label: 'SMF (会话管理功能)' },
    { value: 'amf', label: 'AMF (接入和移动性管理)' },
    { value: 'upf', label: 'UPF (用户面功能)' },
    { value: 'pcf', label: 'PCF (策略控制功能)' },
    { value: 'udm', label: 'UDM (统一数据管理)' },
    { value: 'nrf', label: 'NRF (网络存储功能)' }
  ];

  // 测试核心网日志API
  const testCoreLogAPI = async () => {
    setTestState(prev => ({ ...prev, loading: true, error: null }));
    try {
      console.log(`测试获取 ${testState.selectedNF.toUpperCase()} 日志...`);
      const result = await getCoreLog(testState.selectedNF);
      setTestState(prev => ({
        ...prev,
        coreLogResult: result,
        loading: false
      }));
      console.log('核心网日志结果:', result);
    } catch (error: any) {
      console.error('获取核心网日志失败:', error);
      setTestState(prev => ({
        ...prev,
        error: error.message || '获取日志失败',
        loading: false
      }));
    }
  };

  // 测试基础API
  const testBasicAPIs = async () => {
    setTestState(prev => ({ ...prev, loading: true, error: null }));
    try {
      console.log('测试基础API...');
      
      // 并行获取数据
      const [constellationResult, terminalResult] = await Promise.all([
        getConstellationList(),
        getTerminalList()
      ]);

      setTestState(prev => ({
        ...prev,
        constellationData: constellationResult,
        terminalData: terminalResult,
        loading: false
      }));

      console.log('星座数据:', constellationResult);
      console.log('终端数据:', terminalResult);
    } catch (error: any) {
      console.error('基础API测试失败:', error);
      setTestState(prev => ({
        ...prev,
        error: error.message || '基础API测试失败',
        loading: false
      }));
    }
  };

  // 清除结果
  const clearResults = () => {
    setTestState(prev => ({
      ...prev,
      coreLogResult: null,
      constellationData: null,
      terminalData: null,
      error: null
    }));
  };

  const containerStyle: React.CSSProperties = {
    background: 'linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0c1426 100%)',
    minHeight: '100vh',
    padding: '20px',
    fontFamily: 'Arial, sans-serif'
  };

  const cardStyle: React.CSSProperties = {
    background: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '12px',
    padding: '20px',
    margin: '20px 0',
    color: '#ffffff'
  };

  const buttonStyle: React.CSSProperties = {
    background: 'linear-gradient(45deg, #1890ff, #36cfc9)',
    border: 'none',
    borderRadius: '6px',
    color: 'white',
    padding: '10px 20px',
    margin: '5px',
    cursor: 'pointer',
    fontSize: '14px',
    transition: 'all 0.3s ease'
  };

  const tabStyle = (isActive: boolean): React.CSSProperties => ({
    ...buttonStyle,
    background: isActive 
      ? 'linear-gradient(45deg, #1890ff, #36cfc9)' 
      : 'rgba(255, 255, 255, 0.1)',
    opacity: isActive ? 1 : 0.7
  });

  return (
    <div style={containerStyle}>
      <h1 style={{ color: '#ffffff', textAlign: 'center', marginBottom: '30px' }}>
        🧪 简单测试页面
      </h1>

      {/* 标签页导航 */}
      <div style={{ textAlign: 'center', marginBottom: '30px' }}>
        <button 
          style={tabStyle(activeTab === 'api')}
          onClick={() => setActiveTab('api')}
        >
          API 测试
        </button>
        <button 
          style={tabStyle(activeTab === 'components')}
          onClick={() => setActiveTab('components')}
        >
          组件预览
        </button>
      </div>

      {activeTab === 'api' && (
        <div style={{ maxWidth: '1000px', margin: '0 auto' }}>
          {/* 核心网日志测试 */}
          <div style={cardStyle}>
            <h2 style={{ color: '#36cfc9', marginBottom: '20px' }}>
              📋 核心网日志测试
            </h2>
            
            <div style={{ marginBottom: '20px' }}>
              <label style={{ display: 'block', marginBottom: '10px' }}>
                选择网络功能:
              </label>
              <select 
                value={testState.selectedNF}
                onChange={(e) => setTestState(prev => ({ ...prev, selectedNF: e.target.value }))}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  borderRadius: '4px',
                  color: '#ffffff',
                  padding: '8px 12px',
                  fontSize: '14px'
                }}
              >
                {nfOptions.map(option => (
                  <option key={option.value} value={option.value} style={{ background: '#1a2332' }}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <button 
              style={buttonStyle}
              onClick={testCoreLogAPI}
              disabled={testState.loading}
            >
              {testState.loading ? '获取中...' : `获取 ${testState.selectedNF.toUpperCase()} 日志`}
            </button>

            {testState.coreLogResult && (
              <div style={{ 
                marginTop: '20px', 
                background: 'rgba(0, 0, 0, 0.3)', 
                padding: '15px', 
                borderRadius: '8px',
                maxHeight: '300px',
                overflow: 'auto'
              }}>
                <h4 style={{ color: '#36cfc9' }}>日志结果:</h4>
                <pre style={{ color: '#ffffff', fontSize: '12px', whiteSpace: 'pre-wrap' }}>
                  {JSON.stringify(testState.coreLogResult, null, 2)}
                </pre>
              </div>
            )}
          </div>

          {/* 基础API测试 */}
          <div style={cardStyle}>
            <h2 style={{ color: '#36cfc9', marginBottom: '20px' }}>
              🚀 基础 API 测试
            </h2>
            
            <button 
              style={buttonStyle}
              onClick={testBasicAPIs}
              disabled={testState.loading}
            >
              {testState.loading ? '测试中...' : '测试星座和终端API'}
            </button>

            <div style={{ display: 'flex', gap: '20px', marginTop: '20px' }}>
              {testState.constellationData && (
                <div style={{ flex: 1 }}>
                  <h4 style={{ color: '#36cfc9' }}>星座数据:</h4>
                  <div style={{ 
                    background: 'rgba(0, 0, 0, 0.3)', 
                    padding: '10px', 
                    borderRadius: '6px',
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    <pre style={{ color: '#ffffff', fontSize: '12px' }}>
                      {JSON.stringify(testState.constellationData, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              {testState.terminalData && (
                <div style={{ flex: 1 }}>
                  <h4 style={{ color: '#36cfc9' }}>终端数据:</h4>
                  <div style={{ 
                    background: 'rgba(0, 0, 0, 0.3)', 
                    padding: '10px', 
                    borderRadius: '6px',
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    <pre style={{ color: '#ffffff', fontSize: '12px' }}>
                      {JSON.stringify(testState.terminalData, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 错误显示 */}
          {testState.error && (
            <div style={{
              ...cardStyle,
              background: 'rgba(255, 77, 77, 0.2)',
              border: '1px solid rgba(255, 77, 77, 0.5)'
            }}>
              <h3 style={{ color: '#ff4d4f' }}>❌ 错误信息</h3>
              <p>{testState.error}</p>
            </div>
          )}

          {/* 控制按钮 */}
          <div style={{ textAlign: 'center', marginTop: '30px' }}>
            <button 
              style={{ ...buttonStyle, background: 'rgba(255, 255, 255, 0.2)' }}
              onClick={clearResults}
            >
              清除结果
            </button>
          </div>
        </div>
      )}

      {activeTab === 'components' && (
        <div>
          <CoreNetworkLogsPreview />
        </div>
      )}

      {/* 页脚信息 */}
      <div style={{ 
        textAlign: 'center', 
        marginTop: '50px', 
        color: 'rgba(255, 255, 255, 0.6)',
        fontSize: '12px'
      }}>
        <p>🛰️ Cesium 卫星前端测试页面</p>
        <p>用于测试各种 API 接口和组件预览</p>
      </div>
    </div>
  );
};

export default SimpleTestPage; 