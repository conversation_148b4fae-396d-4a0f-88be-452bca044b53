import React, { useEffect } from 'react';
import useSimulationStore from '../store/simulationStore';
import { NetworkModeConfig, KvmNode } from '../components/Header/settingsatellite/parameterSetAPI';

const SimulationStoreTest: React.FC = () => {
  const {
    networkModeConfig,
    kvmList,
    setNetworkModeConfig,
    setKvmList,
    getNetworkModeConfig,
    getKvmList,
    resetSimulation
  } = useSimulationStore();

  useEffect(() => {
    // 测试数据
    const testNetworkConfig: NetworkModeConfig = {
      mode: '3',
      userLocation: 'Beijing',
      groundStationLocation: 'Shanghai',
      coreNetworkSatelliteId: '1',
      upfSatelliteId: '2',
      additionalUserLocations: ['Guangzhou', 'Shenzhen'],
      terminals: ['Beijing', 'Shanghai', 'Guangzhou', 'Shenzhen', 'Chengdu']
    };

    const testKvmList: KvmNode[] = [
      { kvm_id: 1, node_id: 1, node_type: 'gs' },
      { kvm_id: 6, node_id: 2, node_type: 'gs' },
      { kvm_id: 4, node_id: 1, node_type: 'sat' },
      { kvm_id: 5, node_id: 2, node_type: 'sat' }
    ];

    // 设置测试数据
    setNetworkModeConfig(testNetworkConfig);
    setKvmList(testKvmList);

    console.log('测试数据已设置');
  }, [setNetworkModeConfig, setKvmList]);

  const handleGetData = () => {
    const config = getNetworkModeConfig();
    const list = getKvmList();
    console.log('获取的网络模式配置:', config);
    console.log('获取的KVM列表:', list);
  };

  const handleReset = () => {
    resetSimulation();
    console.log('仿真状态已重置');
  };

  return (
    <div style={{ padding: '20px', background: '#f0f0f0', margin: '20px' }}>
      <h2>SimulationStore 测试页面</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>当前存储的网络模式配置:</h3>
        <pre style={{ background: '#fff', padding: '10px', borderRadius: '4px' }}>
          {JSON.stringify(networkModeConfig, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>当前存储的KVM列表:</h3>
        <pre style={{ background: '#fff', padding: '10px', borderRadius: '4px' }}>
          {JSON.stringify(kvmList, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={handleGetData}
          style={{ 
            marginRight: '10px', 
            padding: '8px 16px', 
            background: '#1890ff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          获取数据 (查看控制台)
        </button>
        
        <button 
          onClick={handleReset}
          style={{ 
            padding: '8px 16px', 
            background: '#ff4d4f', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          重置仿真状态
        </button>
      </div>

      <div style={{ background: '#fff', padding: '10px', borderRadius: '4px' }}>
        <h4>说明:</h4>
        <ul>
          <li>页面加载时会自动设置测试数据</li>
          <li>点击"获取数据"按钮可以在控制台查看通过getter方法获取的数据</li>
          <li>点击"重置仿真状态"按钮会清空所有状态，包括新添加的网络配置和KVM列表</li>
          <li>上方显示的是当前store中实时的状态数据</li>
        </ul>
      </div>
    </div>
  );
};

export default SimulationStoreTest;
