import { create } from 'zustand';
import { NetworkModeConfig, KvmNode } from '../components/Header/settingsatellite/parameterSetAPI';
import { AntennaPatternData } from '../components/Header/settingsatellite/antennaPatternAPI';

// 天线参数接口
interface AntennaParameters {
  antenna_type?: string;
  channel_type?: string;
  beam_num?: number;
  beamwidth?: number;
  [key: string]: any; // 允许其他参数
}

export interface SimulationState {
  // 仿真运行状态
  simulationRunning: boolean;
  // 全屏loading状态
  isLoading: boolean;
  // 仿真启动中状态（防止重复点击）
  isSimulationStarting: boolean;
  // 仿真开始时的星座名称状态
  simulationConstellationName: string;
  // 当前卫星名称状态
  currentSatelliteName: string;
  // 点击对象的ID
  pickedObjectId: string | null;
  // 点击对象的名称
  pickedObjectName: string | null;
  // Cesium时间状态
  cesiumTime: string;
  // 天线参数状态
  antennaParameters: AntennaParameters;
  // 天线图数据状态
  antennaPatternData: AntennaPatternData[];
  // 时间差值 (main.tsx中的diff变量)
  timeDiff: number | null;
  // 网络模式配置状态
  networkModeConfig: NetworkModeConfig | null;
  // KVM节点列表状态
  kvmList: KvmNode[];
  // 星间链路卫星ID数组状态
  satelliteIds: number[];

  // Actions
  setSimulationRunning: (running: boolean) => void;
  setIsLoading: (loading: boolean) => void;
  setIsSimulationStarting: (starting: boolean) => void;
  setSimulationConstellationName: (name: string) => void;
  setCurrentSatelliteName: (name: string) => void;
  setPickedObjectId: (id: string | null) => void;
  setPickedObjectName: (name: string | null) => void;
  setPickedObject: (id: string | null, name: string | null) => void;
  setCesiumTime: (time: string) => void;
  setAntennaParameters: (parameters: AntennaParameters) => void;
  updateAntennaParameter: (key: string, value: any) => void;
  setAntennaPatternData: (data: AntennaPatternData[]) => void;
  setTimeDiff: (diff: number) => void;
  getTimeDiff: () => number | null;
  setNetworkModeConfig: (config: NetworkModeConfig | null) => void;
  setKvmList: (list: KvmNode[]) => void;
  getNetworkModeConfig: () => NetworkModeConfig | null;
  getKvmList: () => KvmNode[];
  setSatelliteIds: (ids: number[]) => void;
  getSatelliteIds: () => number[];

  // 重置所有状态
  resetSimulation: () => void;
}

const useSimulationStore = create<SimulationState>((set, get) => ({
  // 初始状态
  simulationRunning: false,
  isLoading: false,
  isSimulationStarting: false,
  simulationConstellationName: 'kuiper',
  currentSatelliteName: 'kuiper',
  pickedObjectId: null,
  pickedObjectName: null,
  cesiumTime: new Date().toISOString(),
  antennaParameters: {},
  antennaPatternData: [],
  timeDiff: null,
  networkModeConfig: null,
  kvmList: [],
  satelliteIds: [],

  // Actions
  setSimulationRunning: (running) => set({ simulationRunning: running }),
  setIsLoading: (loading) => set({ isLoading: loading }),
  setIsSimulationStarting: (starting) => set({ isSimulationStarting: starting }),
  setSimulationConstellationName: (name) => set({ simulationConstellationName: name }),
  setCurrentSatelliteName: (name) => set({ currentSatelliteName: name }),
  setPickedObjectId: (id) => set({ pickedObjectId: id }),
  setPickedObjectName: (name) => set({ pickedObjectName: name }),
  setPickedObject: (id, name) => set({ pickedObjectId: id, pickedObjectName: name }),
  setCesiumTime: (time) => set({ cesiumTime: time }),
  setAntennaParameters: (parameters) => set({ antennaParameters: parameters }),
  updateAntennaParameter: (key, value) => set((state) => ({
    antennaParameters: { ...state.antennaParameters, [key]: value }
  })),
  setAntennaPatternData: (data) => set({ antennaPatternData: data }),
  setTimeDiff: (diff) => set({ timeDiff: diff }),
  getTimeDiff: () => get().timeDiff,
  setNetworkModeConfig: (config) => set({ networkModeConfig: config }),
  setKvmList: (list) => set({ kvmList: list }),
  getNetworkModeConfig: () => get().networkModeConfig,
  getKvmList: () => get().kvmList,
  setSatelliteIds: (ids) => set({ satelliteIds: ids }),
  getSatelliteIds: () => get().satelliteIds,

  // 重置所有状态
  resetSimulation: () => set({
    simulationRunning: false,
    isLoading: false,
    isSimulationStarting: false,
    pickedObjectId: null,
    pickedObjectName: null,
    cesiumTime: new Date().toISOString(),
    antennaParameters: {},
    antennaPatternData: [],
    timeDiff: null,
    networkModeConfig: null,
    kvmList: [],
    satelliteIds: [],
    // simulationConstellationName: 'kuiper',
    // currentSatelliteName: 'kuiper'
  })
}));



export default useSimulationStore;