import { basisBackendUrl, GET } from "./basicURL";

// API 响应类型定义
export interface GetStatusResponse {
  status: string;
  source: string;
  destination: string;
  constellations: number[];
}

export interface GetConstellationResponse {
  constellation_num: number;
  constellations: string[];
}

export interface GetTerminalResponse {
  terminal_num: number;
  terminals: string[];
}

export interface GetCZMLResponse {
  time: number;
  timeinterval: number;
  czml_num: number;
  czml_data: string[];
}

export interface GetPathCZMLExternal {
  getczml: string;
}

// 用于刷新恢复状态用
const getStateUrl = basisBackendUrl + "/get_status";

/**
 * 获取系统状态
 * @returns Promise<GetStatusResponse> - 系统状态信息
 */
async function getStatus(): Promise<GetStatusResponse> {
  try {
    const response = await GET<GetStatusResponse>(getStateUrl);
    return response;
  } catch (error) {
    console.error('Get status error:', error);
    throw error; 
  }
}

// 获取星座名列表
const getConstellationListUrl = basisBackendUrl + "/get_constellation";

/**
 * 获取星座列表
 * @returns Promise<string[]> - 星座名称数组
 */
async function getConstellationList(): Promise<string[]> {
  try {
    const response = await GET<GetConstellationResponse>(getConstellationListUrl);
    return response.constellations;
  } catch (error) {
    console.error('Get constellation error:', error);
    throw error;
  }
}

// 获取地面终端列表
const getTerminalListUrl = basisBackendUrl + "/get_terminal";

/**
 * 获取终端列表
 * @returns Promise<string[]> - 终端名称数组
 */
async function getTerminalList(): Promise<string[]> {
  try {
    const response = await GET<GetTerminalResponse>(getTerminalListUrl);
    return response.terminals;
  } catch (error) {
    console.error('Get terminal error:', error);
    throw error;
  }
}

// 选择卫星壳层
const getConstellationCzml = basisBackendUrl + "/get_czml";

/**
 * 获取 CZML 数据
 * @returns Promise<{czml_len: number, czml_data: any[]}> - CZML 数据对象
 */
async function getCZMLData(): Promise<{czml_len: number, czml_data: any[]}> {
    try {
        const response = await GET<GetCZMLResponse>(getConstellationCzml, 10000);
        // console.log(response.czml_data);
        const czml: any[] = [];
        response.czml_data.forEach(item => {
            czml.push(JSON.parse(item));
        });
        return {czml_len: response.czml_num, czml_data: czml};
    } catch (error) {
        console.error('Get constellation czml error:', error);
        throw error;
  }
}

// 选择卫星链路
const GetPathCzml = basisBackendUrl + "/get_path_external";

/**
 * 获取路径 CZML 数据（外部）
 * @returns Promise<any> - 解析后的 CZML 数据
 */
async function GetPathCZMLExternal(): Promise<any> {
  try {
    const response = await GET<string>(GetPathCzml);
    // console.log(response);
    const getczml = JSON.parse(response);
    return getczml;
  } catch (error) {
    console.error('Get path czml error:', error);
    throw error;
  }
}

// 选择地面站连接至卫星壳层
const attachTerminal = basisBackendUrl + "/attach_terminal";

// 获取地面站名列表
const getGroundStationList = basisBackendUrl + "/get_groundstation";

// 根据前端传来的星座id，返回对应的czml文件
const getConstellationCzmlById = basisBackendUrl + "/get_constellation_czml_by_id";

// 返回现在已选择的星座
const getCurrentConstellation = basisBackendUrl + "/get_current_constellation";

// 导出所有获取数据的异步函数
export { 
  getConstellationList, 
  getTerminalList, 
  getStatus, 
  getCZMLData,
  GetPathCZMLExternal 
};
