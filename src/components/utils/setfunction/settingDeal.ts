// @ts-nocheck
// import * as Cesium from 'cesium';

// 定义设置处理参数接口
export interface SettingDealParams {
  viewer: any;
  satelliteListRef: any;
  setIsRotate: (value: boolean) => void;
}

// 处理设置项的函数
export const settingDeal = (
  settingName: string,
  settingValue: boolean,
  params: SettingDealParams
): void => {
  const { viewer, satelliteListRef, setIsRotate } = params;

  switch (settingName) {
    case "label":
      satelliteListRef.current.forEach((satellite) => {
        let satelliteEntity = viewer.entities.getById(satellite[0]);
        if (satelliteEntity.label == undefined) {
          satelliteEntity.label.text = satellite[0];
          satelliteEntity.label.font = "14pt Source Han Sans CN";
          satelliteEntity.label.fillColor = Cesium.Color.WHITE;
        } else {
          satelliteEntity.label.show = settingValue;
        }
      });
      break;
    case "icon":
      console.log(satelliteListRef.current);
      
      satelliteListRef.current.forEach((satellite) => {
        let satelliteEntity = viewer.entities.getById(satellite[0]);
        satelliteEntity.billboard.show = settingValue;
        satelliteEntity.model.show = !settingValue;
      });
      break;
    case "model":
      satelliteListRef.current.forEach((satellite) => {
        let satelliteEntity = viewer.entities.getById(satellite[0]);
        satelliteEntity.model.show = settingValue;
        satelliteEntity.billboard.show = !settingValue;
      });
      break;
    case "track":
      satelliteListRef.current.forEach((satellite) => {
        let satelliteEntity = viewer.entities.getById(satellite[0]);
        satelliteEntity.path.show = settingValue;
      });
      break;
    case "light":
      // 开启光照 & 亮度设置: 两种方式
      viewer.scene.globe.enableLighting = settingValue;
      viewer.shadows = settingValue;
      break;
    case "sun":
      // 显示太阳
      viewer.scene.sun.show = settingValue;
      break;
    case "star":
      // 显示星空
      viewer.scene.skyBox.show = settingValue;
      break;
    case "time":
      // 显示时间轴
      viewer.animation.container.style.visibility =
        settingValue == true ? "visible" : "hidden";
      viewer.timeline.container.style.visibility =
        settingValue == true ? "visible" : "hidden";
      break;
    case "rotate":
      // 是否旋转
      setIsRotate(settingValue);
      break;
  }
}; 