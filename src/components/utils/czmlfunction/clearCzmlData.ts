// @ts-nocheck
import { BaseStation } from "../../types/type";
import { BeamCellManager } from "../../features/beamCell";
import { CZML_CLEAR_DELAY } from "../../config/timerConfig";

// 清除CZML数据功能的参数接口
export interface ClearCzmlDataParams {
  viewer: any;
  setSatelliteList: (list: string[]) => void;
  setBaseStationList: (list: BaseStation[]) => void;
  setCurSatellite: (satellite: string) => void;
  setCurBaseStation: (station: BaseStation | null) => void;
  resetSimulation: () => void;
  beamCellManager?: React.MutableRefObject<BeamCellManager | null>; // 添加可选的波束小区管理器
}

// 清除所有CZML数据源
export const clearAllCzmlData = (params: ClearCzmlDataParams) => {
  const {
    viewer,
    setSatelliteList,
    setBaseStationList,
    setCurSatellite,
    setCurBaseStation,
    resetSimulation,
    beamCellManager
  } = params;

  console.log("开始清除所有CZML数据源...");
  
  try {
    // 先停止时钟以避免渲染错误
    if (viewer && viewer.clock) {
      viewer.clock.shouldAnimate = false;
    }
    
    // 等待更长时间，确保渲染循环完全停止
    setTimeout(() => {
      try {
        // 首先清除所有实体
        if (viewer && viewer.entities) {
          viewer.entities.removeAll();
        }
        
        // 安全地逐个移除数据源，而不是使用removeAll()
        if (viewer && viewer.dataSources && viewer.dataSources.length > 0) {
          console.log(`准备移除 ${viewer.dataSources.length} 个数据源`);
          console.log('viewer.dataSources',viewer.dataSources);
          
          // 创建数据源数组的副本
          const dataSourcesArray = [];
          for (let i = 0; i < viewer.dataSources.length; i++) {
            dataSourcesArray.push(viewer.dataSources.get(i));
          }
          
          // 逐个安全移除数据源
          for (let i = dataSourcesArray.length - 1; i >= 0; i--) {
            try {
              const dataSource = dataSourcesArray[i];
              if (dataSource && viewer.dataSources.contains(dataSource)) {
                viewer.dataSources.remove(dataSource);
                console.log(`已移除数据源 ${i}`);
              }
            } catch (removeError) {
              console.warn(`移除数据源 ${i} 时出错:`, removeError);
              // 如果单个移除失败，跳过这个数据源继续下一个
              continue;
            }
          }
          
          console.log("所有数据源移除完成");
        }

        // 清除波束小区图元
        if (beamCellManager && beamCellManager.current) {
          try {
            beamCellManager.current.clearAll();
            console.log("波束小区图元清除完成");
          } catch (cellError) {
            console.warn("清除波束小区图元时出错:", cellError);
          }
        }



        // 重置相关状态
        setSatelliteList([]);
        setBaseStationList([]);
        setCurSatellite("");
        setCurBaseStation(null);
        // 使用 Zustand 重置仿真状态
        resetSimulation();
        
        // 重新启动时钟
        if (viewer && viewer.clock) {
          viewer.clock.shouldAnimate = true;
        }
        
        console.log("CZML数据源清除完成");
      } catch (innerError) {
        console.error("内部清除过程出错:", innerError);

        // 即使出现错误，也尝试清除波束小区图元
        if (beamCellManager && beamCellManager.current) {
          try {
            beamCellManager.current.clearAll();
            console.log("波束小区图元清除完成（错误处理中）");
          } catch (cellError) {
            console.warn("错误处理中清除波束小区图元时出错:", cellError);
          }
        }



        // 发生错误时，强制重置状态但不尝试清除Cesium对象
        setSatelliteList([]);
        setBaseStationList([]);
        setCurSatellite("");
        setCurBaseStation(null);
        resetSimulation();
        
        // 重新启动时钟
        if (viewer && viewer.clock) {
          viewer.clock.shouldAnimate = true;
        }
        
        console.log("由于错误，仅重置了状态");
      }
    }, CZML_CLEAR_DELAY); // 从配置文件获取等待时间，默认200ms
    
  } catch (error) {
    console.error("清除CZML数据时出错:", error);

    // 即使在最外层错误处理中，也尝试清除波束小区图元
    if (beamCellManager && beamCellManager.current) {
      try {
        beamCellManager.current.clearAll();
        console.log("波束小区图元清除完成（最外层错误处理中）");
      } catch (cellError) {
        console.warn("最外层错误处理中清除波束小区图元时出错:", cellError);
      }
    }



    // 最小化错误处理，只重置状态
    setSatelliteList([]);
    setBaseStationList([]);
    setCurSatellite("");
    setCurBaseStation(null);
    resetSimulation();
    
    // 确保时钟继续运行
    if (viewer && viewer.clock) {
      viewer.clock.shouldAnimate = true;
    }
  }
}; 