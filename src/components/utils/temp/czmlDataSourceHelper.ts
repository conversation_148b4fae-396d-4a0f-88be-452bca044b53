import { CzmlUtils } from "./czmlUtils";

// 声明全局 Cesium 对象
declare const Cesium: any;

/**
 * CZML 数据源助手类，用于管理 Cesium 中的 CZML 数据源
 */
class CzmlDataSourceHelper {
    private cesiumViewer: any;
    private czmlDataSource: any;
    private czmlUtils: Map<number, any>;
    private maxCzmlLength: number;

    constructor(cesiumViewer: any) {
        this.cesiumViewer = cesiumViewer;
        this.czmlDataSource = new Cesium.CzmlDataSource();

        // czmlUtils用于存储每个czml对象集合的map，czml的长度最长是3（卫星，地面站，终端），最少是2（卫星，终端）
        this.czmlUtils = new Map<number, any>();
        // czml对象的最大长度
        this.maxCzmlLength = 0;
    }

    /**
     * 设置最大 CZML 长度
     * @param maxCzmlLength - 最大 CZML 长度
     */
    setMaxCzmlLength(maxCzmlLength: number): void {
        this.maxCzmlLength = maxCzmlLength;
    }

    /**
     * 添加 CZML 数据
     * @param czml - CZML 数据对象
     * @param index - 索引
     */
    addCzml(czml: any, index: number): void {
        // if (this.czmlDataSource === null) {
        //     this.czmlDataSource = new Cesium.CzmlDataSource();
        //     this.cesiumViewer.dataSources.add(this.czmlDataSource);
        // }
        
        this.czmlUtils.set(index, czml);
        this.czmlDataSource.process(czml)
            .then((czmlDataObject: any) => {
                console.log(czmlDataObject);
                console.log("load czml success");
            }).catch((err: any) => {
                console.log(err);
            });
    }

    /**
     * 添加 CZML 文件
     * @param czmlfile - CZML 文件数据
     */
    addCzmlfile(czmlfile: any): void {
        // if (this.czmlDataSource === null) {
        //     this.czmlDataSource = new Cesium.CzmlDataSource();
        //     this.cesiumViewer.dataSources.add(this.czmlDataSource);
        // }
        
        // this.czmlUtils.set(index, czml);
        this.czmlDataSource.process(czmlfile)
            .then((czmlDataObject: any) => {
                console.log(czmlDataObject);
                console.log("load czml success");
            }).catch((err: any) => {
                console.log(err);
            });
    }

    /**
     * 清除所有数据源
     */
    clearDataSources(): void {
        // 清除所有已有的czml数据源
        this.cesiumViewer.dataSources.removeAll(true);

        // 建立新的czml数据源
        this.czmlDataSource = new Cesium.CzmlDataSource();
        this.cesiumViewer.dataSources.add(this.czmlDataSource);
    }

    /**
     * 清除路径 CZML 数据源
     * @param postClearFunction - 清除后的回调函数
     */
    clearPathCzmlDataSources(postClearFunction?: () => void): void {
        this.cesiumViewer.dataSources.removeAll(true);

        this.czmlDataSource = new Cesium.CzmlDataSource();
        this.cesiumViewer.dataSources.add(this.czmlDataSource);

        for (let i = 0; i < this.maxCzmlLength; i++) {
            const czmlObject = this.czmlUtils.get(i);
            if (czmlObject !== null && czmlObject !== undefined) {
                this.czmlDataSource.process(czmlObject)
                    .then((czmlDataObject: any) => {
                        console.log("load czml success");
                    }).catch((err: any) => {
                        console.log(err);
                    }).finally(() => {
                        if (postClearFunction) {
                            postClearFunction();
                        }
                    });
            }
        }

        for (let i = this.maxCzmlLength; i < this.czmlUtils.size; i++) {
            this.czmlUtils.delete(i);
        }
    }
}

export { CzmlDataSourceHelper };
