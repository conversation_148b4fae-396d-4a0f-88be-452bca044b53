// 卫星数据相关类型定义

export interface Position {
  x: number;
  y: number;
  z: number;
}

export interface Velocity {
  x: number;
  y: number;
  z: number;
}

export interface SatelliteData {
  satellite_id: string;
  position: Position;
  velocity: Velocity;
  altitude: number;
  ground_stations: string[];
  adjacent_satellites: string[];
  status: string;
  last_updated: string;
  calculation_method?: string;
  beam_status?: string;
}

export interface GroundStationData {
  ground_station_id: string;
  position: {
    latitude: number;
    longitude: number;
    altitude: number;
  };
  connected_satellites: string[];
  beam_connections: BeamConnection[];
  total_capacity: number;
  used_capacity: number;
  active_connections: number;
  status: string;
  last_updated: string;
}

export interface BeamConnection {
  satellite_id: string;
  capacity: number;
  sinr: number;
  channel_gain: number;
  antenna_gain: number;
}
