// 卫星数据API调用相关函数

import { basisBackendUrl } from '../api/basicURL';
import { SatelliteData, GroundStationData } from './types';
import { getCurrentTime, transformBackendData, transformGroundStationData, generateMockSatelliteData, generateMockGroundStationData } from './utils';

/**
 * 调用后端API获取卫星状态
 * @param satelliteId - 卫星ID
 * @param cesiumTime - 当前仿真时间
 */
async function callBackendAPI(satelliteId: string, cesiumTime?: string): Promise<any> {
  const currentTime = getCurrentTime(cesiumTime);
  
  const response = await fetch(`${basisBackendUrl}/get_satellite_status`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      satellite_id: satelliteId,
      time: currentTime
    })
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  
  if (data.status !== 200 || !data.data) {
    throw new Error(data.message || 'Failed to get satellite status');
  }

  return data;
}

/**
 * 调用后端API获取地面站状态
 * @param groundStationId - 地面站ID
 * @param cesiumTime - 当前仿真时间
 */
async function callGroundStationBackendAPI(groundStationId: string, cesiumTime?: string): Promise<any> {
  const groundStationIdNum = parseInt(groundStationId);
  const currentTime = getCurrentTime(cesiumTime);
  
  const response = await fetch(`${basisBackendUrl}/get_ground_station_status`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      ground_station_id: groundStationIdNum,
      time: currentTime
    })
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  
  if (data.status !== 200 || !data.data) {
    throw new Error(data.message || 'Failed to get ground station status');
  }

  return data;
}

/**
 * 获取波束状态数据
 */
export async function fetchBeamStatus(): Promise<Record<string, string>> {
  try {
    const response = await fetch(`${basisBackendUrl}/get_beam_status`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.status !== 200 || !data.data) {
      throw new Error(data.message || 'Failed to get beam status');
    }

    // 检查数据格式并正确解析
    if (data.data && data.data.beam_status) {
      // 如果 beam_status 是字符串，需要解析JSON
      if (typeof data.data.beam_status === 'string') {
        return JSON.parse(data.data.beam_status);
      } else {
        return data.data.beam_status;
      }
    } else {
      // 如果数据直接是期望的格式
      return data.data || {};
    }
  } catch (error) {
    console.error('API获取波束状态失败，生成模拟数据:', error);
    // 返回模拟数据用于演示
    return generateMockBeamStatus();
  }
}

/**
 * 生成模拟波束状态数据
 */
function generateMockBeamStatus(): Record<string, string> {
  return {
    '1': 'activate',
    '2': 'deactivate', 
    '3': 'activate',
    '4': 'activate',
    '5': 'activate',
    '6': 'deactivate'
  };
}

/**
 * 获取卫星数据（主要API函数）
 * @param satelliteId 卫星ID
 * @param cesiumTime 当前仿真时间（可选）
 * @returns Promise<SatelliteData>
 */
export async function fetchSatelliteData(satelliteId: string, cesiumTime?: string): Promise<SatelliteData> {
  try {
    // console.log("Fetching satellite data for ID:", satelliteId);
    const backendData = await callBackendAPI(satelliteId, cesiumTime);
    return transformBackendData(backendData, satelliteId);
  } catch (error) {
    console.error('Error fetching satellite data:', error);
    return generateMockSatelliteData(satelliteId);
  }
}

/**
 * 获取地面站数据（主要API函数）
 * @param groundStationId 地面站ID
 * @param cesiumTime 当前仿真时间（可选）
 * @returns Promise<GroundStationData>
 */
export async function fetchGroundStationData(groundStationId: string, cesiumTime?: string): Promise<GroundStationData> {
  try {
    const backendData = await callGroundStationBackendAPI(groundStationId, cesiumTime);
    return transformGroundStationData(backendData, groundStationId);
  } catch (error) {
    console.error('Error fetching ground station data:', error);
    return generateMockGroundStationData(groundStationId);
  }
}
