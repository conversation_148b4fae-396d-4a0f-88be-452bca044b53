# 卫星数据服务模块

这个模块提供了卫星和地面站数据获取的API调用功能，已从原来的单一文件重构为模块化结构。

## 文件结构

```
src/components/utils/satelliteData/
├── index.ts          # 主入口文件，导出所有公共API
├── types.ts          # TypeScript类型定义
├── constants.ts      # 常量定义
├── api.ts           # API调用相关函数
├── utils.ts         # 工具函数和数据处理
└── README.md        # 本文档
```

## 主要功能

### 1. 类型定义 (types.ts)
- `Position` - 位置坐标接口
- `Velocity` - 速度向量接口
- `SatelliteData` - 卫星数据接口
- `GroundStationData` - 地面站数据接口
- `BeamConnection` - 波束连接接口

### 2. 常量 (constants.ts)
- `UPDATE_FREQUENCY` - 更新频率 (2000ms)
- `EARTH_RADIUS_KM` - 地球半径
- `METERS_TO_KM` - 米到千米转换常数
- 其他物理常量和模拟数据

### 3. API函数 (api.ts)
- `fetchSatelliteData(satelliteId, cesiumTime?)` - 获取卫星数据
- `fetchGroundStationData(groundStationId, cesiumTime?)` - 获取地面站数据
- `fetchBeamStatus()` - 获取波束状态

### 4. 工具函数 (utils.ts)
- 数据转换函数 (位置、速度、高度计算)
- 模拟数据生成函数
- 数据格式化函数

## 使用方法

### 基本导入
```typescript
import { 
  fetchSatelliteData, 
  fetchGroundStationData, 
  SatelliteData, 
  GroundStationData,
  satelliteDataService 
} from '../../utils/satelliteData';
```

### 获取卫星数据
```typescript
const satelliteData = await fetchSatelliteData('SAT-001', cesiumTime);
```

### 获取地面站数据
```typescript
const groundStationData = await fetchGroundStationData('GS-001', cesiumTime);
```

### 使用服务对象（向后兼容）
```typescript
const data = await satelliteDataService.fetchSatelliteData('SAT-001');
```

## 迁移说明

从原来的 `satelliteDataService.ts` 迁移到新的模块结构：

### 旧的导入方式
```typescript
import { fetchSatelliteData, SatelliteData } from './satelliteDataService';
```

### 新的导入方式
```typescript
import { fetchSatelliteData, SatelliteData } from '../../utils/satelliteData';
```

所有的API接口保持不变，只需要更新导入路径即可。

## 特性

- ✅ 模块化设计，便于维护和扩展
- ✅ 完整的TypeScript类型支持
- ✅ 向后兼容，保持原有API不变
- ✅ 自动降级到模拟数据（当API调用失败时）
- ✅ 支持实时时间参数传入
- ✅ 完整的错误处理机制
