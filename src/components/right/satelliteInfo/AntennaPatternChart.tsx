import React, { useEffect, useRef, useCallback } from "react";
import * as echarts from "echarts";
import useSimulationStore from '../../../store/simulationStore';

interface AntennaPatternChartProps {
  objectType: 'satellite' | 'groundstation' | null;
}

const AntennaPatternChart: React.FC<AntennaPatternChartProps> = ({ objectType }) => {
  // 从 store 获取状态
  const simulationRunning = useSimulationStore((state) => state.simulationRunning);
  const isLoading = useSimulationStore((state) => state.isLoading);
  const antennaPatternData = useSimulationStore((state) => state.antennaPatternData);
  
  // 图表相关的 refs
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.EChartsType | null>(null);

  // 判断是否应该显示图表
  const shouldShowChart = simulationRunning && !isLoading && objectType === "satellite" && antennaPatternData.length > 0;

  // 安全清理图表实例的函数
  const safeDisposeChart = useCallback(() => {
    if (chartInstance.current) {
      try {
        if (!chartInstance.current.isDisposed()) {
          chartInstance.current.dispose();
        }
      } catch (error) {
        console.warn("天线图表清理时出现警告:", error);
      } finally {
        chartInstance.current = null;
      }
    }
  }, []);

  // 初始化图表
  useEffect(() => {
    if (shouldShowChart && chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
      console.log("天线方向图初始化完成");
    }

    // 当不应该显示图表时，清理图表实例
    if (!shouldShowChart && chartInstance.current) {
      safeDisposeChart();
    }
  }, [shouldShowChart, safeDisposeChart]);

  // 图表配置选项
  const getChartOption = useCallback(() => {
    // 准备数据
    const angles = antennaPatternData.map(item => item.angle);
    const gains = antennaPatternData.map(item => item.gain);

    return {
      // 背景颜色
      backgroundColor: "rgba(255,255,255, 0)",

      // 标题
      title: {
        text: '天线方向图',
        left: 'center',
        top: '1%',
        textStyle: {
          color: '#fff',
          fontSize: 11, // 减小字体以适应小尺寸
          fontWeight: 'normal'
        }
      },

      // 鼠标悬停显示
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(0,0,0,0.7)",
        textStyle: {
          color: "#fff",
          fontSize: 11,
        },
        formatter: function(params: any) {
          const data = params[0];
          return `角度: ${data.axisValue}°<br/>增益: ${data.value}dB`;
        }
      },

      // 图例 - 调整到左上角
      legend: {
        show: true,
        top: '12%',
        left: '15%', // 移到左上角
        textStyle: {
          color: "white",
          fontSize: 10,
        },
        itemWidth: 12,
        itemHeight: 6,
        data: [
          { name: "天线增益", itemStyle: { color: "rgba(13, 126, 222, 1)" } }
        ],
      },

      // 网格留白 - 调整以显示轴标题
      grid: {
        top: "28%",
        left: "18%", // 增加左边距以显示Y轴标题
        right: "10%",
        bottom: "25%", // 增加底部边距以显示X轴标题
      },

      // 横轴线 (角度)
      xAxis: {
        type: "value",
        name: "角度 (°)",
        nameLocation: "middle",
        nameGap: 20, // 减小间距
        nameTextStyle: {
          color: "#fff",
          fontSize: 10 // 减小字体
        },
        min: Math.min(...angles),
        max: Math.max(...angles),
        axisLine: {
          show: true,
          lineStyle: { color: "rgba(255,255,255,0.5)" },
        },
        axisLabel: {
          textStyle: { color: "#fff", fontSize: 9 }, // 减小标签字体
        },
        splitLine: {
          show: true,
          lineStyle: { color: "rgba(255,255,255,0.1)" },
        },
        axisTick: { show: false },
      },

      // 纵轴线 (增益)
      yAxis: {
        type: "value",
        name: "增益 (dB)",
        nameLocation: "middle",
        nameGap: 35, // 减小间距
        nameTextStyle: {
          color: "#fff",
          fontSize: 10 // 减小字体
        },
        position: "left",
        axisLine: {
          show: true,
          lineStyle: { color: "rgba(255,255,255,0.5)" },
        },
        axisLabel: {
          show: true,
          textStyle: { color: "#fff", fontSize: 9 }, // 减小标签字体
        },
        splitLine: {
          show: true,
          lineStyle: { color: "rgba(255,255,255,0.1)" },
        },
        axisTick: { show: false },
      },

      series: [
        {
          name: "天线增益",
          type: "line",
          symbol: "none",
          smooth: false,
          lineStyle: {
            normal: { 
              width: 1.5, 
              color: "rgba(13, 126, 222, 1)" 
            }
          },
          itemStyle: { 
            color: "rgba(13, 126, 222, 1)" 
          },
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  { offset: 0, color: "rgba(13, 126, 222, 0.3)" },
                  { offset: 1, color: "rgba(13, 126, 222, 0)" },
                ],
                false
              ),
            },
          },
          data: antennaPatternData.map(item => [item.angle, item.gain]),
        },
      ],
    };
  }, [antennaPatternData]);

  // 更新图表数据
  useEffect(() => {
    if (shouldShowChart && chartInstance.current) {
      try {
        if (!chartInstance.current.isDisposed()) {
          const option = getChartOption();
          chartInstance.current.setOption(option);
        }
      } catch (error) {
        console.warn("天线图表更新时出现警告:", error);
        // 如果图表更新失败，尝试重新初始化
        safeDisposeChart();
        if (chartRef.current) {
          chartInstance.current = echarts.init(chartRef.current);
          const option = getChartOption();
          chartInstance.current.setOption(option);
        }
      }
    }
  }, [shouldShowChart, getChartOption, safeDisposeChart]);

  // 组件清理
  useEffect(() => {
    return () => {
      safeDisposeChart();
    };
  }, [safeDisposeChart]);

  // 渲染逻辑 - 只有在卫星模式下才显示
  if (objectType !== "satellite") {
    return null; // 非卫星模式下不显示任何内容
  }

  if (!shouldShowChart) {
    // 添加调试信息

    return null; // 不显示任何内容
  }



  return (
    <div
      style={{
        width: '100%',
        height: '140px', // 进一步减小高度
        marginTop: '6px',
        backgroundColor: 'rgba(0,0,0,0.2)',
        border: '1px solid rgba(255,255,255,0.3)',
        borderRadius: '4px',
        padding: '4px',
        boxSizing: 'border-box',
        flexShrink: 0 // 防止被压缩
      }}
    >
      <div
        ref={chartRef}
        style={{
          height: '100%',
          width: '100%'
        }}
      />
    </div>
  );
};

export default AntennaPatternChart;
