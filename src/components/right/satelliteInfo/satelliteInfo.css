#satellite-info{
    width: 100%;
    height: 400px; /* 减小高度以适应右侧面板 */
    display: flex;
    flex-direction: column;
    padding-left: 1px;
    padding-right: 1px;
    overflow: hidden; /* 防止整体滚动 */
    box-sizing: border-box;
}
#sate-model{
    width: 95%;
    height: 120px; /* 固定高度 */
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    box-sizing: border-box;
    flex-shrink: 0;
    border: 2px solid;
    border-image: linear-gradient(45deg, rgb(15, 155, 141), rgb(157, 180, 217)) 1;
    background-origin: border-box;
    background-clip: content-box, border-box;
    box-shadow: 0 0 1px 1px rgba(247, 247, 247, 0.5);
    margin-left: 15px;
    margin-bottom: 10px;
}

#sate-info{
    width: 100%;
    flex: 1; /* 使用flex布局 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    overflow: hidden; /* 防止溢出 */
    min-height: 0; /* 允许flex子项收缩 */
}

#up{
    width: 100%;
    height: 60px; /* 固定高度 */
    color: white;
    margin-bottom: 5px;
    flex-shrink: 0; /* 不允许收缩 */
}

#bottom{
    width: 100%;
    flex: 1; /* 使用flex布局，占用剩余空间 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    overflow-y: auto; /* 允许垂直滚动 */
    overflow-x: hidden;
    padding: 5px 0px 5px 0;
    box-sizing: border-box;
    min-height: 0; /* 允许收缩 */
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    flex-shrink: 0;
    width: 100%;
    min-width: 0;
    overflow: hidden;
    padding: 0 4px; /* 为边框留出空间 */
}

/* 确保最后一行有额外的底部空间 */
/* .info-row:last-child {
    margin-bottom: 40px;
} */

.info-item {
    flex: 1;
    text-align: center;
    padding: 8px 10px;
    min-width: 0; /* 允许flex项目收缩 */
    max-width: 50%; /* 限制最大宽度 */
    border: 1px solid rgba(22, 196, 198, 0.3); /* 添加边框 */
    border-radius: 6px; /* 圆角边框 */
    margin: 0 4px; /* 左右间距 */
    background: rgba(22, 196, 198, 0.05); /* 轻微背景色 */
    transition: all 0.2s ease; /* 平滑过渡效果 */
}

/* 悬停效果 */
.info-item:hover {
    border-color: rgba(22, 196, 198, 0.6);
    background: rgba(22, 196, 198, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(22, 196, 198, 0.2);
}

.item-label {
    color: #8b8b8b;
    font-size: 14px;
    margin-bottom: 4px;
    word-break: break-word;
    overflow-wrap: break-word;
}

.item-value {
    color: #16c4c6;
    font-size: 16px;
    font-weight: bold;
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.name-item{
    
    font-size: 18px;
    text-align: center;
    /* margin-top: 10px; */
    margin-left: 10px;
    /* margin-bottom: 10px; */
}
.name-sate{
    font-size: 19px;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    justify-content: center; 
}

#launch-time-container{
    width: 33%;
    height: 100%;
    float: left;
    color: #16c4c6;
}
#status-container{
    width: 33%;
    height: 100%;
    float: left;
    color: #3ca2ea;
}
#activity-container{
    width: 33%;
    height: 100%;
    float: left;
    color: #cfd003;
}

.sate-info-img{
    width: 60px;
    height:20px;
}

.name-weather{
    display: flex;
    align-items: center;
    margin-left: 10px;
    justify-content: center; 
    
}
.weather{
    width: 25px;
    height: 25px;
}
.weather-wrap{
    width: 50px;
    height: 40px;
    display: flex;
    line-height: 40px;
    align-items: center;
    margin-left: 20px;
}


/* 自定义滚动条样式 */
#bottom::-webkit-scrollbar {
    width: 8px;
}

#bottom::-webkit-scrollbar-track {
    background: rgba(139, 139, 139, 0.1);
    border-radius: 4px;
    margin: 5px 0; /* 添加上下边距 */
}

#bottom::-webkit-scrollbar-thumb {
    background: rgba(22, 196, 198, 0.6);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#bottom::-webkit-scrollbar-thumb:hover {
    background: rgba(22, 196, 198, 0.8);
}

/* 确保滚动区域有足够的空间 */
#bottom {
    scrollbar-gutter: stable;
}