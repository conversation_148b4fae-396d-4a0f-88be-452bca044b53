#box{
    margin-bottom: 10px;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%);
    border: 1px solid rgba(13, 126, 222, 0.5);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transition: all 0.3s ease;
    animation: boxFadeIn 0.6s ease-out forwards;
}

/* Box出现动画 */
@keyframes boxFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
.boxTitle{
    height: 45px;
    background: linear-gradient(90deg, rgba(13, 126, 222, 0.9) 0%, rgba(13, 126, 222, 0.6) 70%, transparent 100%);
    border-radius: 8px 8px 0 0;
    border-bottom: 2px solid rgba(13, 126, 222, 0.8);
    color: #ffffff;
    letter-spacing: 1px;
    font-size: 16px;
    font-weight: 600;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    vertical-align: middle;
    box-sizing: border-box;
    padding: 12px 20px;
    margin: 0;
    display: flex;
    align-items: center;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    position: relative;
}
/* .cube {
    display: inline-block;
    width: 5px;
    height: 32px;
    background-color: rgba(210, 51, 90, 1);
    margin-right: 15px;
} */

.boxContent{
    background: rgba(0, 20, 40, 0.8);
    box-sizing: border-box;
    padding: 15px 20px 20px 20px;
    color: #ffffff;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    border-radius: 0 0 8px 8px;
    min-height: 60px;
}

/* 悬停效果 */
#box:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    border-color: rgba(13, 126, 222, 0.8);
    transition: all 0.3s ease;
}

/* 标题前的装饰元素 */
.boxTitle::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00d4ff 0%, #0099cc 100%);
    border-radius: 2px;
    margin-right: 12px;
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
}

/* 内容区域文字样式优化 */
.boxContent * {
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 特殊处理Ant Design组件在Box中的显示 */
.boxContent .ant-tabs-tab {
    color: #ffffff !important;
    background: rgba(13, 126, 222, 0.3) !important;
    border: 1px solid rgba(13, 126, 222, 0.6) !important;
    animation: none !important;
    transform: none !important;
}

.boxContent .ant-tabs-tab:hover {
    color: #ffffff !important;
    background: rgba(13, 126, 222, 0.5) !important;
    border-color: rgba(13, 126, 222, 0.8) !important;
}

.boxContent .ant-tabs-tab-active {
    color: #ffffff !important;
    background: rgba(13, 126, 222, 0.7) !important;
    border-color: rgba(13, 126, 222, 1) !important;
}

.boxContent .ant-tabs-tab-btn {
    color: #ffffff !important;
}

/* 防止Box内的组件继承动画 */
.boxContent * {
    animation-duration: 0s !important;
    animation-delay: 0s !important;
    animation-iteration-count: 1 !important;
}

/* 禁用Box内Ant Design组件的省略号功能 */
.boxContent .ant-tabs-nav-operations {
    display: none !important;
}

.boxContent .ant-tabs-nav-more {
    display: none !important;
}

.boxContent .ant-tabs-nav-wrap {
    overflow: visible !important;
}

.boxContent .ant-tabs-tab-btn {
    white-space: nowrap !important;
    overflow: visible !important;
    text-overflow: clip !important;
}

/* 表格样式优化 */
.boxContent table {
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 4px;
}

.boxContent table th,
.boxContent table td {
    border-color: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

/* 图表容器样式优化 */
.boxContent canvas,
.boxContent svg {
    background: rgba(255, 255, 255, 0.02) !important;
    border-radius: 4px;
}

/* 滚动条样式优化 */
.boxContent::-webkit-scrollbar {
    width: 6px;
}

.boxContent::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.boxContent::-webkit-scrollbar-thumb {
    background: rgba(13, 126, 222, 0.6);
    border-radius: 3px;
}

.boxContent::-webkit-scrollbar-thumb:hover {
    background: rgba(13, 126, 222, 0.8);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .boxTitle {
        font-size: 14px;
        padding: 10px 15px;
    }
    
    .boxContent {
        padding: 12px 15px 15px 15px;
    }
}

@media (max-width: 768px) {
    .boxTitle {
        font-size: 12px;
        padding: 8px 12px;
    }
    
    .boxContent {
        padding: 10px 12px 12px 12px;
    }
}

/*图片替换*/
#box-img {
    width: 180px;
    height: 32px;
    background-image: url('../assets/1.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    padding-left: 5px;
}

