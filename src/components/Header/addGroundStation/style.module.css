.addGroundStationModal {
  color: #fff !important;
}

/* 全局强制白色文字 */
.addGroundStationModal * {
  color: #fff !important;
}

/* 保持按钮和特殊元素的颜色 */
.addGroundStationModal .ant-btn-primary {
  color: #fff !important;
}

.addGroundStationModal .ant-tabs-tab-active,
.addGroundStationModal .ant-tabs-tab-active * {
  color: #1890ff !important;
}

.addGroundStationModal .ant-upload-drag-icon * {
  color: #1890ff !important;
}

.addGroundStationModal .ant-modal-content {
  background: #1a1a1a;
  border: 1px solid #333;
}

.addGroundStationModal .ant-modal-header {
  background: #1a1a1a;
  border-bottom: 1px solid #333;
}

.addGroundStationModal .ant-modal-title {
  color: #fff !important;
  font-size: 18px;
  font-weight: bold;
}

.addGroundStationModal .ant-modal-close-x {
  color: #fff !important;
}

.addGroundStationModal .ant-modal-body {
  background: #1a1a1a;
  padding: 24px;
}

/* Tabs 样式 */
.addGroundStationModal .ant-tabs-tab {
  color: #fff !important;
  background: transparent !important;
  border: 1px solid #333 !important;
  margin-right: 8px !important;
}

.addGroundStationModal .ant-tabs-tab-active {
  color: #1890ff !important;
  background: rgba(24, 144, 255, 0.1) !important;
  border-color: #1890ff !important;
}

.addGroundStationModal .ant-tabs-tab:hover {
  color: #1890ff !important;
}

.addGroundStationModal .ant-tabs-content-holder {
  background: transparent;
}

.addGroundStationModal .ant-tabs-ink-bar {
  background: #1890ff;
}

/* Form 样式 */
.form {
  margin-top: 20px;
}

.form input[placeholder*="地面站名称"] {
  background: transparent !important;
  border: 1px solid #fff !important;
  color: #fff !important;
}

.form .ant-form-item-label > label {
  color: #fff !important;
  font-weight: 500;
}

.form .ant-form-item-required::before {
  color: #ff4d4f !important;
}

.form .ant-input,
.form .ant-input-number,
.form .ant-select-selector {
  background: #262626 !important;
  border: 1px solid #fff !important;
  color: #fff !important;
}

/* 确保地面站名称输入框使用相同的背景色 */

.form .ant-input:focus,
.form .ant-input-number:focus,
.form .ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.form .ant-input::placeholder,
.form .ant-input-number input::placeholder {
  color: #999 !important;
}

.form .ant-input-number .ant-input-number-input {
  background-color: transparent !important;
  color: #fff !important;
}

.form .ant-select {
  background-color: #262626 !important;
}

.form .ant-select .ant-select-selector {
  background-color: #262626 !important;
  border: 1px solid #fff !important;
  color: #fff !important;
}

.form .ant-select .ant-select-selection-item {
  color: #fff !important;
}

.form .ant-select .ant-select-arrow {
  color: #fff !important;
}

/* Upload 组件样式 */
.uploader {
  background: #262626 !important;
  border: 2px dashed #444 !important;
  border-radius: 6px;
}

.uploader:hover {
  border-color: #1890ff !important;
}

.uploader .ant-upload-drag-icon {
  color: #1890ff !important;
}

.uploader .ant-upload-text {
  color: #fff !important;
  font-size: 14px;
  font-weight: 500;
}

.uploader .ant-upload-hint {
  color: #fff !important;
  font-size: 12px;
}

/* Button 样式 */
.submitButton {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #fff;
  width: 100%;
  height: 40px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
  margin-top: 10px;
}

.submitButton:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.submitButton:focus {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Select 下拉框样式 */
.form .ant-select-dropdown {
  background: #262626 !important;
  border: 1px solid #fff !important;
}

.form .ant-select-item {
  color: #fff !important;
}

.form .ant-select-item-option-selected {
  background: rgba(24, 144, 255, 0.2) !important;
  color: #fff !important;
}

.form .ant-select-item:hover {
  background: rgba(24, 144, 255, 0.1) !important;
}

/* TextArea 样式 */
.form .ant-input {
  background: #262626 !important;
  border: 1px solid #fff !important;
  color: #fff !important;
  resize: vertical;
}

.form .ant-input:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 输入数字组件特殊样式 */
.form .ant-input-number-input {
  background: transparent !important;
  color: #fff !important;
}

.form .ant-input-number-handler-wrap {
  background: #333 !important;
  border-left: 1px solid #fff !important;
}

.form .ant-input-number-handler {
  border-color: #fff !important;
  color: #fff !important;
}

.form .ant-input-number-handler:hover {
  color: #1890ff !important;
}

/* 消息提示框样式 */
.ant-message .ant-message-notice {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
  color: #fff !important;
}

.ant-message .ant-message-success .anticon {
  color: #52c41a !important;
}

.ant-message .ant-message-error .anticon {
  color: #ff4d4f !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .addGroundStationModal {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .form {
    margin-top: 16px;
  }
} 
