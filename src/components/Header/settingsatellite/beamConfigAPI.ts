import useSimulationStore from '../../../store/simulationStore';
import { basisBackendUrl, POST } from '../../utils/api/basicURL';
// API基础URL - 根据实际后端地址调整
const API_BASE_URL = basisBackendUrl;

// API响应类型定义
interface ApiResponse {
  success: boolean;
  message?: string;
}

interface BeamNumResponse extends ApiResponse {
  beam_num?: number;
}

interface BeamwidthResponse extends ApiResponse {
  beamwidth?: number;
}

interface ParametersResponse extends ApiResponse {
  parameters?: { [key: string]: any };
}

interface AntennaTypeResponse extends ApiResponse {
  antenna_type?: string;
}

interface ChannelTypeResponse extends ApiResponse {
  channel_type?: string;
}

interface SidelobeResponse extends ApiResponse {
  data?: {
    sidelobe_angle: number;
    sidelobe_gain: number;
  };
  parameters?: {
    angle_step: number;
    max_search_angle: number;
  };
}

/**
 * 设置天线类型
 * @param antennaType 天线类型值
 * @returns Promise<boolean> 是否成功
 */
export async function setAntennaType(antennaType: string): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/set_antenna_type`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        antenna_type: antennaType
      }),
    });

    const result: ApiResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    console.log('天线类型设置成功:', antennaType);
    return result.success;
  } catch (error) {
    console.error('设置天线类型失败:', error);
    return false;
  }
}

/**
 * 设置信道类型
 * @param channelType 信道类型值
 * @returns Promise<boolean> 是否成功
 */
export async function setChannelType(channelType: string): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/set_channel_type`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        channel_type: channelType
      }),
    });

    const result: ApiResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    console.log('信道类型设置成功:', channelType);
    return result.success;
  } catch (error) {
    console.error('设置信道类型失败:', error);
    return false;
  }
}

/**
 * 设置波束数量
 * @param beamNum 波束数量值
 * @returns Promise<boolean> 是否成功
 */
export async function setBeamNum(beamNum: number): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/set_beam_num`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        beam_num: beamNum
      }),
    });

    const result: ApiResponse = await response.json();
    // console.log('设置波束数量返回结果', result);
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    console.log('波束数量设置成功:', beamNum);
    return result.success;
  } catch (error) {
    console.error('设置波束数量失败:', error);
    return false;
  }
}

/**
 * 计算3dB波束宽度
 * @returns Promise<number | null> 波束宽度值，失败返回null
 */
export async function calculate3dbBeamwidth(): Promise<number | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/calculate_3db_beamwidth`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result: BeamwidthResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    if (result.success && result.beamwidth !== undefined) {
      console.log('波束宽度计算成功:', result.beamwidth);
      return result.beamwidth;
    }
    
    return null;
  } catch (error) {
    console.error('计算波束宽度失败:', error);
    return null;
  }
}

/**
 * 获取当前所有参数
 * @returns Promise<{[key: string]: any} | null> 参数对象，失败返回null
 */
export async function getCurrentParameters(): Promise<{[key: string]: any} | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/get_beam_current_parameters`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result: ParametersResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    if (result.success && result.parameters) {
    //   console.log('获取当前参数成功:', result.parameters);
      return result.parameters;
    }
    
    return null;
  } catch (error) {
    console.error('获取当前参数失败:', error);
    return null;
  }
}

/**
 * 获取旁瓣数据
 * @returns Promise<{sidelobe_angle: number, sidelobe_gain: number} | null> 旁瓣数据，失败返回null
 */
export async function getSidelobe(): Promise<{sidelobe_angle: number, sidelobe_gain: number} | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/get_sidelobe`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result: SidelobeResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    if (result.success && result.data) {
    //   console.log('获取旁瓣数据成功:', result.data);
      return result.data;
    }
    
    return null;
  } catch (error) {
    console.error('获取旁瓣数据失败:', error);
    return null;
  }
}

/**
 * 配置波束参数的主函数
 * 按照要求的顺序执行所有API调用，并将结果保存到store中
 * @param antennaType 天线类型
 * @param channelModel 信道模型  
 * @param beamCount 波束数量
 * @returns Promise<boolean> 是否全部成功
 */
export async function configureBeamParameters(
  antennaType: string,
  channelModel: string,
  beamCount: number
): Promise<boolean> {
  console.log('开始配置波束参数:', { antennaType, channelModel, beamCount });
  
  try {
    // 第一步：设置天线类型
    const antennaSuccess = await setAntennaType(antennaType);
    if (!antennaSuccess) {
      console.error('设置天线类型失败');
      return false;
    }

    // 第二步：设置信道类型
    const channelSuccess = await setChannelType(channelModel);
    if (!channelSuccess) {
      console.error('设置信道类型失败');
      return false;
    }

    // 第三步：设置波束数量
    const beamSuccess = await setBeamNum(beamCount);
    if (!beamSuccess) {
      console.error('设置波束数量失败');
      return false;
    }

    console.log('所有设置请求完成，开始获取计算结果...');

    // 第四步：获取波束宽度
    const beamwidth = await calculate3dbBeamwidth();
    if (beamwidth === null) {
      console.error('获取波束宽度失败');
      return false;
    }

    // 第五步：获取所有当前参数
    const allParameters = await getCurrentParameters();
    if (allParameters === null) {
      console.error('获取当前参数失败');
      return false;
    }

    // 第六步：获取旁瓣数据
    const sidelobeData = await getSidelobe();
    if (sidelobeData === null) {
      console.error('获取旁瓣数据失败');
      return false;
    }

    // 第七步：保存到store中
    const { setAntennaParameters } = useSimulationStore.getState();
    
    const parametersToSave = {
      antenna_type: antennaType,
      channel_type: channelModel,
      beam_num: beamCount,
      beamwidth: beamwidth,
      sidelobe: sidelobeData, // 将旁瓣数据保存为sidelobe字段
      ...allParameters // 包含所有其他参数
    };

    setAntennaParameters(parametersToSave);
    
    console.log('波束参数配置完成并已保存到store:', parametersToSave);
    return true;

  } catch (error) {
    console.error('配置波束参数过程中发生异常:', error);
    return false;
  }
}

/**
 * 获取可用的天线类型列表
 * @returns Promise<string[] | null> 天线类型列表，失败返回null
 */
export async function getAvailableAntennaTypes(): Promise<string[] | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/get_available_antenna_types`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result: { success: boolean; antenna_types?: string[]; message?: string } = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    if (result.success && result.antenna_types) {
      // console.log('获取可用天线类型成功:', result.antenna_types);
      return result.antenna_types;
    }
    
    return null;
  } catch (error) {
    console.error('获取可用天线类型失败:', error);
    return null;
  }
}

/**
 * 获取可用的信道类型列表
 * @returns Promise<string[] | null> 信道类型列表，失败返回null
 */
export async function getAvailableChannelTypes(): Promise<string[] | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/get_available_channel_types`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result: { success: boolean; channel_types?: string[]; message?: string } = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    if (result.success && result.channel_types) {
      // console.log('获取可用信道类型成功:', result.channel_types);
      return result.channel_types;
    }
    
    return null;
  } catch (error) {
    console.error('获取可用信道类型失败:', error);
    return null;
  }
} 