import { notification } from 'antd';
import { basisBackendUrl, POST } from '../../utils/api/basicURL';

// 定义算法类型
type AlgorithmKey = 'a' | 'b' | 'c' | 'd';

// 算法映射关系
const ALGORITHM_MAPPING: Record<AlgorithmKey, string> = {
  'a': 'closest_satellite',    // 最近卫星
  'b': 'north_first',          // 北向优先  
  'c': 'random',               // 随机选择
  'd': 'south_first'           // 南向优先
};

// 算法中文名称映射
const ALGORITHM_NAME_MAPPING: Record<AlgorithmKey, string> = {
  'a': '最近卫星',
  'b': '北向优先',
  'c': '随机选择', 
  'd': '南向优先'
};

/**
 * 设置卫星选择算法
 * @param algorithm 算法值（现在直接使用后端算法值，如 'closest_satellite', 'north_first' 等）
 * @returns Promise<boolean> 设置是否成功
 */
export const setSatelliteChooseMethod = async (algorithm: string): Promise<boolean> => {
  try {
    // 验证算法值不为空
    if (!algorithm) {
      console.error('算法值不能为空');
      notification.error({
        message: '设置失败',
        description: '算法值不能为空'
      });
      return false;
    }

    console.log(`设置卫星选择算法: ${algorithm}`);

    // 构建API URL
    const apiUrl = basisBackendUrl + '/set_satellite_choose_method';

    // 构建请求数据
    const requestData = {
      satellite_choose_method: algorithm
    };

    // 调用后端API
    const response = await POST(apiUrl, requestData);

    console.log('卫星选择算法设置成功:', response);
    notification.success({
      message: '设置成功',
      description: `卫星选择算法已设置为: ${algorithm}`
    });
    return true;
  } catch (error) {
    console.error('设置卫星选择算法异常:', error);
    notification.error({
      message: '设置失败',
      description: '网络异常，请检查网络连接'
    });
    return false;
  }
};

/**
 * 获取算法的中文名称
 * @param algorithm 前端算法值
 * @returns 算法的中文名称
 */
export const getAlgorithmName = (algorithm: string): string => {
  if (algorithm in ALGORITHM_NAME_MAPPING) {
    return ALGORITHM_NAME_MAPPING[algorithm as AlgorithmKey];
  }
  return '未知算法';
};

/**
 * 获取算法的后端值
 * @param algorithm 前端算法值
 * @returns 后端API需要的算法值
 */
export const getBackendAlgorithm = (algorithm: string): string => {
  if (algorithm in ALGORITHM_MAPPING) {
    return ALGORITHM_MAPPING[algorithm as AlgorithmKey];
  }
  return '';
}; 