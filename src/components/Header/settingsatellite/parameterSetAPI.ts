// 参数设置API - 调用 /api/parameter_set 接口
import { notification } from 'antd';

// KVM节点接口定义
export interface KvmNode {
  kvm_id: number;
  node_id: number;
  node_type: 'gs' | 'sat';
}

// 参数设置请求接口定义
export interface ParameterSetRequest {
  kvm_list: KvmNode[];
  mode: number;
  time: number;
  extend_nodes: number;
  start: number;
}

// 参数设置响应接口定义
export interface ParameterSetResponse {
  status: number;
  message: string;
}

// 网络模式配置参数接口
export interface NetworkModeConfig {
  mode: string;
  userLocation: string;
  groundStationLocation: string;
  coreNetworkSatelliteId?: string;
  upfSatelliteId?: string;
  additionalUserLocations: string[];
  terminals: string[];
}

/**
 * 根据网络模式配置生成KVM列表
 * @param config 网络模式配置
 * @returns KVM节点列表
 */
export const generateKvmList = (config: NetworkModeConfig): KvmNode[] => {
  const kvmList: KvmNode[] = [];
  const { mode, userLocation, groundStationLocation, coreNetworkSatelliteId, upfSatelliteId, additionalUserLocations, terminals } = config;

  // 获取终端在列表中的索引（+1作为node_id）
  const getUserLocationNodeId = (location: string) => terminals.indexOf(location) ;

  switch (mode) {
    case '1': // 透明模式 - 需要选择地面节点：1（用户位置）、2（地面站位置）
      // kvm_id 1 为用户位置
      if (userLocation) {
        kvmList.push({
          kvm_id: 1,
          node_id: getUserLocationNodeId(userLocation),
          node_type: 'gs'
        });
      }
      // kvm_id 2 为地面站位置
      if (groundStationLocation) {
        kvmList.push({
          kvm_id: 2,
          node_id: getUserLocationNodeId(groundStationLocation),
          node_type: 'gs'
        });
      }
      break;

    case '2': // 再生模式 - 需要选择地面节点：1（用户位置）、6（地面站位置）
      // kvm_id 1 为用户位置
      if (userLocation) {
        kvmList.push({
          kvm_id: 1,
          node_id: getUserLocationNodeId(userLocation),
          node_type: 'gs'
        });
      }
      // kvm_id 6 为地面站位置
      if (groundStationLocation) {
        kvmList.push({
          kvm_id: 6,
          node_id: getUserLocationNodeId(groundStationLocation),
          node_type: 'gs'
        });
      }
      break;

    case '3': // 再生+部分核心网上星 - 地面节点：1（用户位置）、6（地面站位置）；卫星节点：4（核心网卫星）、5（UPF卫星）
      // kvm_id 1 为用户位置
      if (userLocation) {
        kvmList.push({
          kvm_id: 1,
          node_id: getUserLocationNodeId(userLocation),
          node_type: 'gs'
        });
      }
      // kvm_id 6 为地面站位置
      if (groundStationLocation) {
        kvmList.push({
          kvm_id: 6,
          node_id: getUserLocationNodeId(groundStationLocation),
          node_type: 'gs'
        });
      }
      // kvm_id 4 为核心网卫星
      if (coreNetworkSatelliteId) {
        kvmList.push({
          kvm_id: 4,
          node_id: parseInt(coreNetworkSatelliteId),
          node_type: 'sat'
        });
      }
      // kvm_id 5 为UPF卫星
      if (upfSatelliteId) {
        kvmList.push({
          kvm_id: 5,
          node_id: parseInt(upfSatelliteId),
          node_type: 'sat'
        });
      }
      break;

    case '4': // 再生+全量核心网上星 - 地面节点：1（用户位置）、6（地面站位置）；卫星节点：5（UPF卫星）
      // kvm_id 1 为用户位置
      if (userLocation) {
        kvmList.push({
          kvm_id: 1,
          node_id: getUserLocationNodeId(userLocation),
          node_type: 'gs'
        });
      }
      // kvm_id 6 为地面站位置
      if (groundStationLocation) {
        kvmList.push({
          kvm_id: 6,
          node_id: getUserLocationNodeId(groundStationLocation),
          node_type: 'gs'
        });
      }
      // kvm_id 5 为UPF卫星
      if (upfSatelliteId) {
        kvmList.push({
          kvm_id: 5,
          node_id: parseInt(upfSatelliteId),
          node_type: 'sat'
        });
      }
      break;

    default:
      console.warn('未知的网络模式:', mode);
      break;
  }

  // 添加额外用户位置（kvm_id从11开始）
  additionalUserLocations.forEach((location, index) => {
    if (location) {
      kvmList.push({
        kvm_id: 11 + index,
        node_id: getUserLocationNodeId(location),
        node_type: 'gs'
      });
    }
  });

  return kvmList;
};

/**
 * 调用参数设置API
 * 发送两次请求：第一次到4999端口(start=0)，只有第一次成功才会继续第二次到5000端口(start=1)
 * @param config 网络模式配置
 * @returns Promise<boolean> 是否成功
 */
export const setParameterSet = async (config: NetworkModeConfig): Promise<boolean> => {
  try {
    // 生成KVM列表
    const kvmList = generateKvmList(config);

    // 验证必要参数
    if (kvmList.length === 0) {
      notification.error({
        message: '参数错误',
        description: '请至少选择一个节点位置'
      });
      return false;
    }

    // 构建基础请求参数
    const baseRequestData = {
      kvm_list: kvmList,
      mode: parseInt(config.mode),
      time: 100000000, // 默认时间
      extend_nodes: config.additionalUserLocations.filter(loc => loc).length,
    };

    // 第一次请求：发送到 4999 端口，start 设置为 0
    const firstRequestData: ParameterSetRequest = {
      ...baseRequestData,
      start: 0
    };

    console.log('发送第一次参数设置请求 (4999端口):', firstRequestData);

    const firstResponse = await fetch('http://************:4999/api/parameter_set', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(firstRequestData)
    });

    if (!firstResponse.ok) {
      throw new Error(`第一次请求失败! HTTP status: ${firstResponse.status}`);
    }

    const firstResult: ParameterSetResponse = await firstResponse.json();

    if (firstResult.status !== 200) {
      notification.error({
        message: '第一次参数设置失败',
        description: firstResult.message || '服务器返回错误状态'
      });
      console.error('第一次参数设置失败:', firstResult);
      return false;
    }

    console.log('第一次参数设置成功:', firstResult);

    // 只有第一次请求成功时才继续执行第二次请求
    // 第二次请求：发送到 5000 端口，start 设置为 1
    const secondRequestData: ParameterSetRequest = {
      ...baseRequestData,
      start: 1
    };

    console.log('发送第二次参数设置请求 (5000端口):', secondRequestData);

    const secondResponse = await fetch('http://************:5000/api/parameter_set', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(secondRequestData)
    });

    if (!secondResponse.ok) {
      throw new Error(`第二次请求失败! HTTP status: ${secondResponse.status}`);
    }

    const secondResult: ParameterSetResponse = await secondResponse.json();

    if (secondResult.status === 200) {
      // notification.success({
      //   message: '参数设置成功',
      //   description: '两次参数设置请求均已成功完成'
      // });
      console.log('第二次参数设置成功:', secondResult);
      return true;
    } else {
      notification.error({
        message: '第二次参数设置失败',
        description: secondResult.message || '服务器返回错误状态'
      });
      console.error('第二次参数设置失败:', secondResult);
      return false;
    }

  } catch (error) {
    console.error('参数设置API调用失败:', error);
    notification.error({
      message: '参数设置失败',
      description: '网络请求失败，请检查网络连接'
    });
    return false;
  }
};

/**
 * 验证网络模式配置
 * @param config 网络模式配置
 * @returns 验证结果和错误信息
 */
export const validateNetworkModeConfig = (config: NetworkModeConfig): { isValid: boolean; errorMessage?: string } => {
  const { mode, userLocation, groundStationLocation, coreNetworkSatelliteId, upfSatelliteId, additionalUserLocations } = config;

  // 检查用户位置和地面站位置不能相同
  if (userLocation && groundStationLocation && userLocation === groundStationLocation) {
    return { isValid: false, errorMessage: '用户位置和地面站位置不能相同' };
  }

  // 检查额外用户位置数量限制
  if (additionalUserLocations.length > 10) {
    return { isValid: false, errorMessage: '额外用户位置数量不能超过10个' };
  }

  // 检查额外用户位置不能重复
  const validAdditionalLocations = additionalUserLocations.filter(loc => loc);
  const uniqueLocations = new Set(validAdditionalLocations);
  if (validAdditionalLocations.length !== uniqueLocations.size) {
    return { isValid: false, errorMessage: '额外用户位置不能重复' };
  }

  // 根据模式检查必要参数
  switch (mode) {
    case '1':
    case '2':
      if (!userLocation || !groundStationLocation) {
        return { isValid: false, errorMessage: '请选择用户位置和地面站位置' };
      }
      break;
    case '3':
      if (!userLocation || !groundStationLocation || !coreNetworkSatelliteId || !upfSatelliteId) {
        return { isValid: false, errorMessage: '请选择用户位置、地面站位置、核心网卫星和UPF卫星' };
      }
      // 验证卫星编号范围
      const coreNetworkId = parseInt(coreNetworkSatelliteId);
      const upfId = parseInt(upfSatelliteId);
      if (isNaN(coreNetworkId) || coreNetworkId < 0 || coreNetworkId > 1000) {
        return { isValid: false, errorMessage: '核心网卫星编号必须在0-1000范围内' };
      }
      if (isNaN(upfId) || upfId < 0 || upfId > 1000) {
        return { isValid: false, errorMessage: 'UPF卫星编号必须在0-1000范围内' };
      }
      break;
    case '4':
      if (!userLocation || !groundStationLocation || !upfSatelliteId) {
        return { isValid: false, errorMessage: '请选择用户位置、地面站位置和UPF卫星' };
      }
      // 验证卫星编号范围
      const upfId4 = parseInt(upfSatelliteId);
      if (isNaN(upfId4) || upfId4 < 0 || upfId4 > 1000) {
        return { isValid: false, errorMessage: 'UPF卫星编号必须在0-1000范围内' };
      }
      break;
    default:
      return { isValid: false, errorMessage: '请选择有效的网络模式' };
  }

  return { isValid: true };
};
