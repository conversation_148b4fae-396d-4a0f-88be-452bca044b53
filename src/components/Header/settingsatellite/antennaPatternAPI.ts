// 天线图数据API - 调用 /api/get_gain_list 接口

// 天线图数据响应接口定义
export interface AntennaPatternResponse {
  success: boolean;
  message?: string;
  angles?: number[];
  gain_values?: number[];
  data?: AntennaPatternData[];
}

// 天线图数据项接口定义
export interface AntennaPatternData {
  angle: number;
  gain: number;
}

/**
 * 获取天线图数据
 * @param angle 角度参数，默认为180
 * @param step 步长参数，默认为1
 * @returns Promise<AntennaPatternData[] | null> 天线图数据数组，失败返回null
 */
export async function getAntennaPatternData(angle: number = 180, step: number = 1): Promise<AntennaPatternData[] | null> {
  try {
    console.log('开始获取天线图数据，参数:', { angle, step });

    const response = await fetch('http://10.176.26.78:5001/api/get_gain_list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        angle: angle,
        step: step
      }),
    });

    console.log('API响应状态:', response.status, response.statusText);

    const result = await response.json();
    console.log('API响应数据:', result);

    if (!response.ok) {
      throw new Error(result.message || `HTTP错误! 状态: ${response.status}`);
    }

    // 检查不同可能的响应格式
    if (result.success && result.angles && result.gain_values) {
      // API返回的格式: {success: true, angles: [...], gain_values: [...]}
      const antennaPatternData: AntennaPatternData[] = result.angles.map((angle: number, index: number) => ({
        angle: angle,
        gain: result.gain_values![index]
      }));
      // console.log('获取天线图数据成功 (angles/gain_values格式):', antennaPatternData);
      return antennaPatternData;
    } else if (result.success && result.data) {
      // console.log('获取天线图数据成功 (data格式):', result.data);
      return result.data;
    } else if (Array.isArray(result)) {
      // 如果直接返回数组
      // console.log('获取天线图数据成功 (数组格式):', result);
      return result;
    } else if (result.data && Array.isArray(result.data)) {
      // 如果data字段包含数组
      // console.log('获取天线图数据成功 (data数组格式):', result.data);
      return result.data;
    }

    console.log('未识别的响应格式:', result);
    return null;
  } catch (error) {
    console.error('获取天线图数据失败，详细错误:', error);
    return null;
  }
}
