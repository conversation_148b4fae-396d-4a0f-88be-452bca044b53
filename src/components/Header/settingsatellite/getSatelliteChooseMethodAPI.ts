import { notification } from 'antd';
import { basisBackendUrl, GET } from '../../utils/api/basicURL';

// 定义 API 响应接口
interface SatelliteChooseMethodResponse {
  algorithm: string;
  full_config: {
    satellite_choosing_algorithm: string;
  };
  supported_algorithms: string[];
}



/**
 * 获取卫星选择算法配置
 * @returns Promise<SatelliteChooseMethodResponse | null> 返回算法配置数据或null
 */
export const getSatelliteChooseMethod = async (): Promise<SatelliteChooseMethodResponse | null> => {
  try {
    // 构建API URL
    const apiUrl = basisBackendUrl + '/get_satellite_choose_method';
    
    console.log('获取卫星选择算法配置:', apiUrl);

    // 调用后端API
    const response = await GET<SatelliteChooseMethodResponse>(apiUrl);
    
    console.log('卫星选择算法配置获取成功:', response);
    return response;
    
  } catch (error) {
    console.error('获取卫星选择算法配置异常:', error);
    notification.error({
      message: '获取失败',
      description: '无法获取卫星选择算法配置，请检查网络连接'
    });
    return null;
  }
};

/**
 * 获取所有支持的算法选项（用于下拉框）
 * @returns Promise<Array<{value: string, label: string}> | null> 返回算法选项数组或null
 */
export const getSupportedAlgorithmOptions = async (): Promise<Array<{value: string, label: string}> | null> => {
  try {
    const response = await getSatelliteChooseMethod();
    if (response && response.supported_algorithms) {
      return response.supported_algorithms.map(algorithm => ({
        value: algorithm,
        label: algorithm  // 直接使用从API获取的算法名称
      }));
    }
    return null;
  } catch (error) {
    console.error('获取支持的算法选项异常:', error);
    return null;
  }
};
