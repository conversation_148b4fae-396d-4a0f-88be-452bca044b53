import React from 'react';
import styles from './Logo.module.css';

interface LogoProps {}

const Logo: React.FC<LogoProps> = () => {
  const logoData = [
    {
      src: "/images/logo/logo.png",
      alt: "logo",
      content: "主要标志"
    },
    {
      src: "/images/logo/spacenet.png", 
      alt: "spacenet",
      content: "SpaceNet"
    }
  ];

  return (
    <div className={styles.logoContainer}>
      {logoData.map((logo, index) => (
        <img 
          key={index}
          src={logo.src} 
          alt={logo.alt}
          title={logo.content}
          className={styles.logoImg}
        />
      ))}
    </div>
  );
};

export default Logo; 