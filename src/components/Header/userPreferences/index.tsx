import React, { useState } from 'react';
import { Modal, Tabs, Form, Input, Button, Select, InputNumber, message } from 'antd';
import styles from './style.module.css';
import {
  createCustomChannel,
  type CustomChannelData
} from './channelAPI';

const { TabPane } = Tabs;
const { Option } = Select;

interface UserPreferencesProps {
  visible: boolean;
  onClose: () => void;
}

const UserPreferences: React.FC<UserPreferencesProps> = ({
  visible,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState('customChannel');
  const [createChannelForm] = Form.useForm();
  const [loading, setLoading] = useState(false);



  // 创建自定义信道
  const handleCreateChannel = async (values: any) => {
    const channelData: CustomChannelData = {
      channel_name: values.channelName,
      path_loss_exponent: values.pathLossExponent || 2.0,
      shadowing_std: values.shadowingStd || 0.0,
      fading_factor: values.fadingFactor || 1.0,
      additional_loss: values.additionalLoss || 0.0,
      custom_params: {
        environment: values.environment || '',
        frequency: values.frequency || 0
      }
    };

    try {
      setLoading(true);
      const result = await createCustomChannel(channelData);
      
      if (result.success) {
        message.success(result.message || '自定义信道创建成功');
        createChannelForm.resetFields();
      } else {
        message.error(result.message || '创建失败');
      }
    } catch (error: any) {
      message.error(`创建失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };



  const handleClose = () => {
    createChannelForm.resetFields();
    onClose();
  };

  return (
    <Modal
      title="首选项"
      visible={visible}
      onCancel={handleClose}
      footer={null}
      width={800}
      centered
      className={styles.userPreferencesModal}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="自定义信道模型" key="customChannel">
          <div className={styles.tabContent}>
            {/* 创建自定义信道表单 */}
            <div className={styles.section}>
              <h3>创建自定义信道</h3>
              <Form
                form={createChannelForm}
                layout="vertical"
                onFinish={handleCreateChannel}
                className={styles.form}
              >
                <Form.Item
                  name="channelName"
                  label="信道名称"
                  rules={[{ required: true, message: '请输入信道名称' }]}
                >
                  <Input placeholder="请输入自定义信道名称" />
                </Form.Item>

                <div className={styles.formRow}>
                  <Form.Item
                    name="pathLossExponent"
                    label="路径损耗指数"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={1.0}
                      max={6.0}
                      step={0.1}
                      placeholder="默认: 2.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>

                  <Form.Item
                    name="shadowingStd"
                    label="阴影衰落标准差"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={0}
                      max={20}
                      step={0.1}
                      placeholder="默认: 0.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </div>

                <div className={styles.formRow}>
                  <Form.Item
                    name="fadingFactor"
                    label="衰落因子"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={0}
                      max={2}
                      step={0.01}
                      placeholder="默认: 1.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>

                  <Form.Item
                    name="additionalLoss"
                    label="额外损耗(dB)"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={0}
                      max={50}
                      step={0.1}
                      placeholder="默认: 0.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </div>

                <div className={styles.formRow}>
                  <Form.Item
                    name="environment"
                    label="环境类型"
                    className={styles.formItem}
                  >
                    <Select placeholder="选择环境类型">
                      <Option value="urban">城市</Option>
                      <Option value="rural">农村</Option>
                      <Option value="suburban">郊区</Option>
                      <Option value="satellite">卫星</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="frequency"
                    label="频率(Hz)"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={0}
                      step={1000000}
                      placeholder="例如: 2400000000"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </div>

                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    className={styles.submitButton}
                  >
                    创建信道
                  </Button>
                </Form.Item>
              </Form>
            </div>


          </div>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default UserPreferences;
