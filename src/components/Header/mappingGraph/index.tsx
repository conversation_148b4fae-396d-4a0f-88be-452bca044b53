import React from 'react';
import { Modal } from 'antd';
import { NetworkModeConfig, KvmNode } from '../settingsatellite/parameterSetAPI';
import styles from './style.module.css';

import mode1 from './images/mode1.png';
import mode2 from './images/mode2.png';
import mode3 from './images/mode3.png';
import mode4 from './images/mode4.png';
import UE from './images/UE.png';
import Ground from './images/Ground.png';
import Core from './images/Core.png';
import UPF from './images/UPF.png';

const logoImages: Record<string, string> = {
  '1': mode1,
  '2': mode2,
  '3': mode3,
  '4': mode4,
  UE: UE,
  Ground: Ground,
  Core: Core,
  UPF: UPF
};

interface MappingGraphProps {
  visible: boolean;
  onClose: () => void;
  networkModeConfig?: NetworkModeConfig;
  kvmList?: KvmNode[];
}

const MappingGraph: React.FC<MappingGraphProps> = ({ visible, onClose, networkModeConfig, kvmList }) => {
  const hasData = networkModeConfig && kvmList && kvmList.length > 0;

  // 获取节点类型对应的图标key
  const getIconKey = (item?: KvmNode) => {
    if (!item) return '';
    if (item.kvm_id === 1) return 'UE';
    if (item.kvm_id === 2 || item.kvm_id === 6) return 'Ground';
    if (item.kvm_id === 4) return 'Core';
    if (item.kvm_id === 5) return 'UPF';
    return '';
  };

  // 按顺序渲染kvmList
  const displayList: (KvmNode | undefined)[] = kvmList ? [...kvmList] : [];

  return (
    <Modal
      title={<span style={{ color: 'white'}}>映射关系图</span>}
      visible={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      centered 
      bodyStyle={{ minHeight: 400, padding: 0 }}
      destroyOnClose
      className={styles.mappingGraphModal}
    >
      {hasData ? (
        <div className={styles.container}>
          {/* 上半部分：根据networkModeConfig.mode展示图片 */}
          <div className={styles.modeImageSection}>
            <img
              src={logoImages[networkModeConfig!.mode]}
              alt={`mode${networkModeConfig!.mode}`}
              className={styles.modeImage}
              onError={e => { (e.target as HTMLImageElement).src = mode1; }}
            />
          </div>
          {/* 下半部分：按kvmList顺序渲染 */}
          <div className={styles.kvmSection}>
            {displayList.map((item, idx) => {
              if (!item) {
                // 空列
                return (
                  <div key={idx} className={styles.emptyKvmItem}>
                    <div className={styles.iconPlaceholder} />
                    <div className={styles.emptyText}>-</div>
                    <div className={styles.emptyText}>-</div>
                  </div>
                );
              }
              const iconKey = getIconKey(item);
              return (
                <div key={idx} className={styles.kvmItem}>
                  {/* 图标 */}
                  <div className={styles.iconContainer}>
                    {iconKey ? (
                      <img 
                        src={logoImages[iconKey]} 
                        alt={iconKey} 
                        className={styles.nodeIcon}
                      />
                    ) : (
                      <div className={styles.iconPlaceholder} />
                    )}
                  </div>
                  {/* kvm_id */}
                  <div className={styles.kvmLabel}>
                    虚拟机 {item.kvm_id}
                  </div>
                  {/* node_id */}
                  <div className={styles.nodeLabel}>
                    节点 {item.node_id}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ) : (
        <div className={styles.emptyDataContainer}>
          暂无映射关系数据
        </div>
      )}
    </Modal>
  );
};

export default MappingGraph;
