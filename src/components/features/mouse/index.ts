// 导入语句必须在文件顶部
import { handleLeftClick, handleRightClick, handleMouseMove } from './mouseHandlers';
import { MouseHandlerDependencies } from './types';

// 声明全局 Cesium 类型
declare const Cesium: any;

// 导出所有鼠标事件处理函数
export { handleLeftClick, handleRightClick, handleMouseMove } from './mouseHandlers';

// 导出链路状态API
export {
  getLinkStatus,
  getProcessedLinkStatus,
  linkStatusCache,
  processLinkStatusData
} from './linkStatusAPI';

// 导出类型定义
export type {
  LeftClickHandlerParams,
  RightClickHandlerParams,
  MouseMoveHandlerParams,
  MouseHandlerDependencies
} from './types';

// 导出链路状态API类型
export type {
  ApiResponse,
  LinkStatusResponse,
  ProcessedLinkStatus
} from './linkStatusAPI';

export const setupMouseHandlers = (dependencies: MouseHandlerDependencies) => {
  const {
    viewer,
    setCurSatellite,
    setNowSystemDate,
    setSatellitePostionData,
    wgs84ToCartesignWrapper,
    nowPicksatellite,
    setPickedObject,
    beamCellManager,
    selectedSatelliteEntity
  } = dependencies;

  // 创建事件处理器
  const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

  // 设置左键点击事件
  const leftClickHandler = handleLeftClick({
    viewer,
    setCurSatellite,
    wgs84ToCartesignWrapper,
    nowPicksatellite,
    setPickedObject,
    beamCellManager,
    selectedSatelliteEntity
  });
  handler.setInputAction(leftClickHandler, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  // 设置右键点击事件
  const rightClickHandler = handleRightClick({
    viewer,
    setNowSystemDate,
    setSatellitePostionData,
    nowPicksatellite,
    beamCellManager,
    selectedSatelliteEntity
  });
  handler.setInputAction(rightClickHandler, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

  // 设置鼠标移动事件
  const mouseMoveHandler = handleMouseMove({
    viewer
  });
  handler.setInputAction(mouseMoveHandler, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  // 返回handler以便需要时销毁
  return handler;
};