/**
 * 链路状态API模块
 * 用于获取卫星链路的实时状态信息
 */

// 获取基础后端URL配置
const ipAddress = "************";
const BACKEND_URL = `http://${ipAddress}:5001`;

/**
 * 后端API响应接口
 */
export interface ApiResponse {
  data: {
    availability: string;
    load: number;
    signal_strength: number;
  };
  message: string;
  status: number;
}

/**
 * 链路状态数据接口
 */
export interface LinkStatusResponse {
  availability: string;
  load: number;
  signal_strength: number;
}

/**
 * 处理后的链路状态数据接口
 */
export interface ProcessedLinkStatus {
  signalStrength: string;
  linkLoadPercentage: string;
  linkStatus: string;
  linkStatusColor: string;
}

/**
 * 获取链路状态数据
 * @returns Promise<LinkStatusResponse> 链路状态数据
 */
export async function getLinkStatus(): Promise<LinkStatusResponse> {
  try {
    const response = await fetch(`${BACKEND_URL}/api/get_link_status_gen`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    const apiResponse: ApiResponse = await response.json();
    // console.log('链路状态API响应:', apiResponse);

    // 检查API响应状态
    if (apiResponse.status !== 200 || !apiResponse.data) {
      throw new Error(apiResponse.message || '获取链路状态失败');
    }

    return apiResponse.data;
  } catch (error) {
    console.error('获取链路状态失败:', error);
    throw error;
  }
}

/**
 * 处理链路状态数据，转换为显示格式
 * @param rawData 原始API响应数据
 * @returns ProcessedLinkStatus 处理后的显示数据
 */
export function processLinkStatusData(rawData: LinkStatusResponse): ProcessedLinkStatus {
  // 处理信号强度 (dBm)
  const signalStrength = `${rawData.signal_strength.toFixed(2)} dBm`;
  
  // 处理链路负载 (百分比)
  const linkLoadPercentage = `${rawData.load.toFixed(2)}%`;
  
  // 处理可用性状态
  let linkStatus: string;
  let linkStatusColor: string;
  
  switch (rawData.availability.toLowerCase()) {
    case 'available':
      linkStatus = '正常';
      linkStatusColor = '#00ff00';
      break;
    case 'unavailable':
      linkStatus = '异常';
      linkStatusColor = '#ff0000';
      break;
    case 'degraded':
      linkStatus = '降级';
      linkStatusColor = '#ffff00';
      break;
    default:
      linkStatus = '未知';
      linkStatusColor = '#888888';
  }

  return {
    signalStrength,
    linkLoadPercentage,
    linkStatus,
    linkStatusColor
  };
}

/**
 * 获取并处理链路状态数据的便捷函数
 * @returns Promise<ProcessedLinkStatus> 处理后的链路状态数据
 */
export async function getProcessedLinkStatus(): Promise<ProcessedLinkStatus> {
  try {
    const rawData = await getLinkStatus();
    return processLinkStatusData(rawData);
  } catch (error) {
    console.error('获取处理后的链路状态失败:', error);
    // 返回默认值
    return {
      signalStrength: 'N/A',
      linkLoadPercentage: 'N/A',
      linkStatus: 'N/A',
      linkStatusColor: '#888888'
    };
  }
}

/**
 * 缓存管理类，用于避免频繁的API调用
 */
class LinkStatusCache {
  private cache: ProcessedLinkStatus | null = null;
  private lastUpdateTime: number = 0;
  private readonly CACHE_DURATION = 2000; // 2秒缓存

  /**
   * 获取缓存的链路状态数据，如果缓存过期则重新获取
   * @returns Promise<ProcessedLinkStatus>
   */
  async getCachedLinkStatus(): Promise<ProcessedLinkStatus> {
    const now = Date.now();
    
    if (this.cache && (now - this.lastUpdateTime) < this.CACHE_DURATION) {
      return this.cache;
    }

    try {
      this.cache = await getProcessedLinkStatus();
      this.lastUpdateTime = now;
      return this.cache;
    } catch (error) {
      // 如果API调用失败，返回缓存的数据（如果有的话）
      if (this.cache) {
        console.warn('API调用失败，使用缓存数据');
        return this.cache;
      }
      throw error;
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache = null;
    this.lastUpdateTime = 0;
  }
}

// 导出缓存实例
export const linkStatusCache = new LinkStatusCache();
