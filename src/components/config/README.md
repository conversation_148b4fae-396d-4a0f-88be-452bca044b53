# 定时器配置管理

本目录包含项目中所有定时器的集中配置管理。

## 文件结构

```
src/components/config/
├── timerConfig.ts      # 主配置文件
├── timerConfig.test.ts # 配置测试文件
└── README.md          # 本文档
```

## 重构说明

### 重构前的问题
- 定时器参数分散在各个文件中，难以统一管理
- 硬编码的时间值不易维护和调整
- 缺乏类型安全和验证机制

### 重构后的改进
- ✅ 集中管理所有定时器配置
- ✅ 提供TypeScript类型安全
- ✅ 支持配置验证
- ✅ 便于运行时动态调整
- ✅ 向后兼容，保持原有功能不变

## 配置项说明

### 数据获取定时器 (dataFetch)

| 配置项 | 默认值 | 说明 | 使用位置 |
|--------|--------|------|----------|
| `MAIN_CLOCK_SYNC_INTERVAL` | 500ms | 主时钟同步间隔 | `main.tsx` |
| `SATELLITE_LIST_UPDATE_INTERVAL` | 800ms | 卫星列表更新间隔 | `satelliteInfoList.tsx` |
| `SATELLITE_DETAIL_UPDATE_INTERVAL` | 800ms | 卫星详细信息更新间隔 | `satelliteInfo.tsx`, `satelliteData/constants.ts` |
| `NETWORK_STATE_CHART_UPDATE_INTERVAL` | 200ms | 网络状态图表更新间隔 | `networkStateAPI.ts` |
| `LINK_STATUS_TEST_UPDATE_INTERVAL` | 2000ms | 链路状态测试更新间隔 | `LinkStatusAPITest.tsx` |
| `LEGACY_SATELLITE_INFO_UPDATE_INTERVAL` | 2000ms | 旧版卫星信息更新间隔 | `temp/satelliteInfo.js` |
| `MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL` | 200ms | 鼠标悬停链路状态更新间隔 | `mouseHandlers.ts` |

### 延迟定时器 (delays)

| 配置项 | 默认值 | 说明 | 使用位置 |
|--------|--------|------|----------|
| `CZML_CLEAR_DELAY` | 200ms | CZML数据清除延迟 | `clearCzmlData.ts` |
| `TEST_WAIT_DELAY` | 300ms | 测试等待延迟 | `test.tsx` |

### API超时配置 (apiTimeouts)

| 配置项 | 默认值 | 说明 | 使用位置 |
|--------|--------|------|----------|
| `DEFAULT_API_TIMEOUT` | 1000000ms | 默认API请求超时 | `basicURL.ts` |

## 使用方法

### 基本导入
```typescript
import { 
  MAIN_CLOCK_SYNC_INTERVAL,
  SATELLITE_LIST_UPDATE_INTERVAL,
  CZML_CLEAR_DELAY 
} from '../config/timerConfig';
```

### 使用示例
```typescript
// 设置定时器
const interval = setInterval(() => {
  fetchData();
}, SATELLITE_LIST_UPDATE_INTERVAL);

// 设置延迟
setTimeout(() => {
  clearData();
}, CZML_CLEAR_DELAY);
```

### 动态配置
```typescript
import { updateTimerConfig, TIMER_CONFIG } from '../config/timerConfig';

// 运行时更新配置
updateTimerConfig({
  dataFetch: {
    MAIN_CLOCK_SYNC_INTERVAL: 1000 // 改为1秒
  }
});
```

### 配置验证
```typescript
import { validateTimerConfig } from '../config/timerConfig';

const result = validateTimerConfig(myConfig);
if (!result.valid) {
  console.error('配置验证失败:', result.errors);
}
```

## 重构的文件列表

以下文件已完成定时器配置重构（共10个文件）：

1. **src/components/main/main.tsx**
   - 重构: `setInterval(..., 500)` → `setInterval(..., MAIN_CLOCK_SYNC_INTERVAL)`

2. **src/components/left/satelliteInfoList/satelliteInfoList.tsx**
   - 重构: `setInterval(..., 800)` → `setInterval(..., SATELLITE_LIST_UPDATE_INTERVAL)`

3. **src/components/right/satelliteInfo/satelliteInfo.tsx**
   - 重构: `satelliteDataService.UPDATE_FREQUENCY` → `SATELLITE_DETAIL_UPDATE_INTERVAL`

4. **src/components/left/networkState/networkStateAPI.ts**
   - 重构: `UPDATE_FREQUENCY = 200` → `UPDATE_FREQUENCY = NETWORK_STATE_CHART_UPDATE_INTERVAL`

5. **src/components/utils/satelliteData/constants.ts**
   - 重构: `UPDATE_FREQUENCY = 800` → `UPDATE_FREQUENCY = SATELLITE_DETAIL_UPDATE_INTERVAL`

6. **src/components/utils/czmlfunction/clearCzmlData.ts**
   - 重构: `setTimeout(..., 200)` → `setTimeout(..., CZML_CLEAR_DELAY)`

7. **src/test/LinkStatusAPITest.tsx**
   - 重构: `setInterval(..., 2000)` → `setInterval(..., LINK_STATUS_TEST_UPDATE_INTERVAL)`

8. **src/components/main/temp/satelliteInfo.js**
   - 重构: `UPDATE_FREQUENCY = 2000` → `UPDATE_FREQUENCY = LEGACY_SATELLITE_INFO_UPDATE_INTERVAL`

9. **src/test/test.tsx**
   - 重构: `setTimeout(..., 300)` → `setTimeout(..., TEST_WAIT_DELAY)`

10. **src/components/features/mouse/mouseHandlers.ts**
    - 重构: `setInterval(..., 200)` → `setInterval(..., MOUSE_HOVER_LINK_STATUS_UPDATE_INTERVAL)`

## 测试

运行测试以验证配置的正确性：

```bash
npm test timerConfig.test.ts
```

## 注意事项

1. **向后兼容**: 所有重构都保持了原有的功能和时间间隔
2. **类型安全**: 使用TypeScript接口确保配置的类型安全
3. **验证机制**: 提供配置验证函数，防止无效配置
4. **性能考虑**: 配置在模块加载时初始化，运行时开销最小

## 未来扩展

- 支持环境变量覆盖配置
- 添加配置热重载功能
- 支持更细粒度的配置分组
- 添加配置变更监听器
