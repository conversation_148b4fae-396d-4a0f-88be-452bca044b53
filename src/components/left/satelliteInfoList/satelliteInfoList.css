.satellite-info-list-wrapper {
  width: 100%;
  max-width: 20vw; /* 与左侧面板宽度保持一致 */
  height: 100%;
  max-height: 350px;
  overflow: hidden; /* 外层容器隐藏溢出 */
  display: flex;
  flex-direction: column;
}

.satellite-info-list-table-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto; /* 同时支持水平和垂直滚动 */
  background: rgba(0, 20, 40, 0.6);
  border: 1px solid rgba(13, 126, 222, 0.4);
  border-radius: 4px;
  padding: 8px;
  /* 设置最小尺寸以确保滚动区域 */
  min-height: 300px;
  max-height: 350px;
  /* Firefox滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(13, 126, 222, 0.6) rgba(255, 255, 255, 0.1);
}

/* 自定义滚动条样式 */
.satellite-info-list-table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.satellite-info-list-table-wrapper::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.satellite-info-list-table-wrapper::-webkit-scrollbar-thumb {
  background: rgba(13, 126, 222, 0.6);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.satellite-info-list-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(13, 126, 222, 0.8);
}

/* 滚动条角落 */
.satellite-info-list-table-wrapper::-webkit-scrollbar-corner {
  background: rgba(0, 20, 40, 0.6);
}

/* 确保表格内容不换行，支持水平滚动 */
.satellite-info-list-table-wrapper .ant-table-thead > tr > th,
.satellite-info-list-table-wrapper .ant-table-tbody > tr > td {
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
  min-width: 80px; /* 确保列有最小宽度 */
  padding: 8px 12px;
}

/* 表格样式优化 */
.satellite-info-list-table-wrapper .ant-table {
  background: transparent;
  color: #ffffff;
  min-width: max-content; /* 确保表格能触发水平滚动 */
}

.satellite-info-list-table-wrapper .ant-table-container {
  background: transparent;
}

.satellite-info-list-table-wrapper .ant-table-header {
  background: transparent;
}

.satellite-info-list-table-wrapper .ant-table-body {
  background: transparent;
}

.satellite-info-list-table-wrapper .ant-table-thead > tr > th {
  background: rgba(13, 126, 222, 0.3);
  border-bottom: 1px solid rgba(13, 126, 222, 0.6);
  color: #ffffff;
  font-weight: bold;
  text-align: center;
}

.satellite-info-list-table-wrapper .ant-table-tbody > tr > td {
  background: rgba(0, 20, 40, 0.4);
  border-bottom: 1px solid rgba(13, 126, 222, 0.2);
  color: #ffffff;
  text-align: center !important;
}

/* 强制所有表格单元格内容居中 */
.satellite-info-list-table-wrapper .ant-table-cell {
  text-align: center !important;
}

.satellite-info-list-table-wrapper .ant-table-thead .ant-table-cell {
  text-align: center !important;
}

.satellite-info-list-table-wrapper .ant-table-tbody .ant-table-cell {
  text-align: center !important;
}

.satellite-info-list-table-wrapper .ant-table-tbody > tr:hover > td {
  background: rgba(13, 126, 222, 0.3);
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .satellite-info-list-wrapper {
    max-width: 18vw;
  }
  
  .satellite-info-list-table-wrapper .ant-table-thead > tr > th,
  .satellite-info-list-table-wrapper .ant-table-tbody > tr > td {
    min-width: 70px;
    padding: 6px 8px;
    font-size: 12px;
  }
}

@media screen and (max-width: 768px) {
  .satellite-info-list-wrapper {
    max-width: 25vw;
  }
  
  .satellite-info-list-table-wrapper .ant-table-thead > tr > th,
  .satellite-info-list-table-wrapper .ant-table-tbody > tr > td {
    min-width: 60px;
    padding: 4px 6px;
    font-size: 10px;
  }
}

/* 确保表格列标题不被省略 */
.satellite-info-list-table-wrapper .ant-table-thead > tr > th .ant-table-column-title {
  overflow: visible;
  text-overflow: clip;
  white-space: nowrap;
}

/* 优化表格分页器样式（如果有的话） */
.satellite-info-list-table-wrapper .ant-pagination {
  margin-top: 16px;
  text-align: center;
}

.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-item,
.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-prev,
.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-next {
  background: rgba(13, 126, 222, 0.3);
  border-color: rgba(13, 126, 222, 0.6);
}

.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-item a,
.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-prev .ant-pagination-item-link,
.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-next .ant-pagination-item-link {
  color: #ffffff;
}

.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-item:hover,
.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-prev:hover,
.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-next:hover {
  background: rgba(13, 126, 222, 0.5);
  border-color: rgba(13, 126, 222, 0.8);
}

.satellite-info-list-table-wrapper .ant-pagination .ant-pagination-item-active {
  background: rgba(13, 126, 222, 0.7);
  border-color: rgba(13, 126, 222, 1);
}

/* 表格特定样式 */
.satellite-table {
  background: transparent !important;
}

.satellite-table .ant-table-container {
  background: transparent !important;
}

.satellite-table .ant-table-content {
  background: transparent !important;
}

/* 固定列样式 */
.satellite-table .ant-table-cell-fix-left {
  background: rgba(13, 126, 222, 0.4) !important;
  border-right: 1px solid rgba(13, 126, 222, 0.6) !important;
}

.satellite-table .ant-table-cell-fix-left:before {
  background-color: rgba(13, 126, 222, 0.3) !important;
}

/* 表格内容滚动优化 */
.satellite-table .ant-table-body {
  scrollbar-width: thin;
  scrollbar-color: rgba(13, 126, 222, 0.6) rgba(255, 255, 255, 0.1);
}

.satellite-table .ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.satellite-table .ant-table-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.satellite-table .ant-table-body::-webkit-scrollbar-thumb {
  background: rgba(13, 126, 222, 0.6);
  border-radius: 3px;
}

.satellite-table .ant-table-body::-webkit-scrollbar-thumb:hover {
  background: rgba(13, 126, 222, 0.8);
}

.satellite-table .ant-table-body::-webkit-scrollbar-corner {
  background: rgba(0, 20, 40, 0.6);
} 