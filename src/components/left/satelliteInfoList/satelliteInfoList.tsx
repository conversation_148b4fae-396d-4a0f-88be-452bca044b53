import { Table } from 'antd';
import React, { useEffect, useState } from 'react';
import './satelliteInfoList.css';
import useSimulationStore from '../../../store/simulationStore';
import { getPathSatelliteExternal } from './satelliteAPI';
import { SATELLITE_LIST_UPDATE_INTERVAL } from '../../config/timerConfig';

type sateLLiteInfoList = {
    satelliteList: string[]
}

const SatelliteInfoList: React.FC<sateLLiteInfoList> = (props) => {
    const {satelliteList} = props;
    
    // 使用 Zustand store 获取仿真状态
    const {
        simulationRunning,
        isLoading,
        currentSatelliteName,
        simulationConstellationName,
        getTimeDiff,
        satelliteIds,
        setSatelliteIds
    } = useSimulationStore();

    // 使用默认值，如果 store 中没有设置星座名称
    // console.log('currentSatelliteName:'+currentSatelliteName)
    const constellationName = simulationConstellationName || "中国移动";
    const [data, setData] = useState<any[]>([]);

    // 动态生成卫星名称列表，基于从API获取的卫星列表
    const satelliteName = satelliteIds.length > 0
        ? satelliteIds.map(satelliteNumber => `${constellationName}_${String(satelliteNumber).padStart(5, '0')}`)
        : Array.from({length: 10}, (_, index) =>
            `${constellationName}_${String(index + 1).padStart(5, '0')}`
        );

    const columns = [
        {
            title: <div style={{textAlign: 'center'}}>name</div>,
            // title:"Satellite",
            dataIndex: "satellite",
            key: "satellite",
            align: "center" as const,
            // render: (text: string) => (
            //     <div style={{ textAlign: 'center', width: '100%' }}>
            //         {text}
            //     </div>
            // ),
        },
        // {
        //     title: <div style={{textAlign: 'center'}}>Az</div>,
        //     dataIndex: "az",
        //     key: "az",
        //     align: "center" as const,
        // },
        // {
        //     title: <div style={{textAlign: 'center'}}>EI</div>,
        //     dataIndex: "ei",
        //     key: "ei",
        //     align: "center" as const,
        // },
        // {
        //     title: <div style={{textAlign: 'center'}}>Dir</div>,
        //     dataIndex: "dir",
        //     key: "dir",
        //     align: "center" as const,
        // },
        // {
        //     title: <div style={{textAlign: 'center'}}>Range</div>,
        //     dataIndex: "range",
        //     key: "range",
        //     align: "center" as const,
        // },
        // {
        //     title: <div style={{textAlign: 'center'}}>Alt</div>,
        //     dataIndex: "alt",
        //     key: "alt",
        //     align: "center" as const,
        // },
        // {
        //     title: <div style={{textAlign: 'center'}}>Orbit</div>,
        //     dataIndex: "orbit",
        //     key: "orbit",
        //     align: "center" as const,
        // }
    ];

    // 获取星间链路卫星数据的函数
    const fetchSatelliteIds = async () => {
        try {
            const timeDiff = getTimeDiff();
            if (timeDiff === null) {
                console.warn('时间差为空，使用默认时间');
                return;
            }

            // console.log('正在调用 API，时间参数:', timeDiff);
            const response = await getPathSatelliteExternal(timeDiff);
            // console.log('API 完整响应:', response);

            if (response && response.satellites && Array.isArray(response.satellites)) {
                setSatelliteIds(response.satellites);
                // console.log('成功设置卫星列表:', response.satellites);
                // console.log('卫星数量:', response.satellites.length);
            } else {
                // console.warn('API返回的卫星数据格式不正确:', response);
                // console.log('响应类型:', typeof response);
                // console.log('satellites字段:', response?.satellites);
            }
        } catch (error) {
            // console.error('获取卫星数据失败:', error);
            // 失败时保持原有的默认行为
        }
    };

    // 生成数据的函数
    const generateData = () => {
        // 如果仿真没有运行或正在加载，不生成数据
        if (!simulationRunning || isLoading) {
            setData([]);
            return;
        }
        
        let dataTemp: any = [];
        satelliteName.forEach((satellite, idx) => {
            // 基于低地球轨道卫星参数生成更合理的数据
            // 轨道高度约520km的低地球轨道卫星
            let baseAz = (idx * 36 + Math.random() * 360) % 360; // 方位角更加随机分布
            let az = (baseAz + (Math.random()-0.5)*10).toFixed(2).toString()+"°"; // 适度波动
            
            // 低轨卫星仰角范围通常在10-80度之间
            let baseEi = 15 + Math.random() * 50; // 基础仰角15-65度
            let ei = (baseEi + (Math.random()-0.5)*15).toFixed(2).toString()+"°";
            
            let dir = Math.random()>0.5?"↑":"↓";
            
            // 距离基于520km轨道高度，加上地球半径约6371km
            let baseRange = 520 + 6371; // 约6891km
            let range = Math.round(baseRange + (Math.random()-0.5)*200); // 适度波动
            
            // 轨道高度约520km
            let baseAlt = 520;
            let alt = Math.round(baseAlt + (Math.random()-0.5)*50); // 小幅波动
            
            // 低轨卫星轨道周期约95分钟（1.5小时左右）
            let basePeriod = 95;
            let orbit = Math.round(basePeriod + (Math.random()-0.5)*5); // 小幅波动
            
            dataTemp.push({
                key: idx.toString(),
                satellite: satellite,
                az:az,
                ei:ei,
                dir:dir,
                range:range,
                alt:alt,
                orbit:orbit
            });
        });
        setData([...dataTemp]);
    };

    // useEffect(() => {
    //     setInit(true);
    // }, []);

    // 监听satelliteIds变化，重新生成数据
    useEffect(() => {
        generateData();
    }, [satelliteIds, simulationRunning, isLoading, constellationName]);

    // 监听simulationRunning和isLoading变化，获取卫星数据
    useEffect(() => {
        if (simulationRunning && !isLoading) {
            fetchSatelliteIds(); // 获取卫星数据
        }
    }, [simulationRunning, isLoading]);

    // 启动定时器，定期更新数据
    useEffect(() => {
        if (simulationRunning && !isLoading) {
            const interval = setInterval(() => {
                fetchSatelliteIds(); // 定期获取卫星数据
            }, SATELLITE_LIST_UPDATE_INTERVAL);

            return () => clearInterval(interval); // 清理定时器
        }
    }, [simulationRunning, isLoading, constellationName]);

    return (
        <div className="satellite-info-list-table-wrapper" style={{width:'100%', height:'350px', overflowY:"auto"}}>
            {isLoading ? (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                    color: '#666',
                    fontSize: '14px'
                }}>
                    正在加载数据...
                </div>
            ) : simulationRunning ? (
                <Table
                    columns={columns}
                    dataSource={data}
                    pagination={false}
                    size="small"
                />
            ) : (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                    color: '#666',
                    fontSize: '14px'
                }}>
                    请先点击'开始仿真'按钮开始数据模拟
                </div>
            )}
        </div>
    );
};

export default SatelliteInfoList;